import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:khelnet/common_widgets/custom_app_bar.dart';
import 'package:khelnet/modules/private_route/fees/controller/get_students_for_fees/get_students_for_fees_state.dart';
import 'package:khelnet/modules/private_route/fees/view/multiple_select_student_screen.dart';
import 'package:khelnet/modules/private_route/fees/view/widget/fees_std_card_helper.dart';
import 'package:khelnet/utils/constants/color_constant.dart';
import 'package:khelnet/utils/extension/navigation_extension.dart';
import 'package:khelnet/utils/theme/typography.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';

import '../../../../common_widgets/custom_svg_picture.dart';
import '../../../../common_widgets/failure_widget.dart';
import '../../../../common_widgets/loader_helper.dart';
import '../../../../common_widgets/multiple_selection_bottom_sheet.dart';
import '../../../../common_widgets/search_field.dart';
import '../../../../common_widgets/single_selection_bottom_sheet.dart';
import '../../../../global/constants/app_constant.dart';
import '../../../../global/constants/gradient_text.dart';
import '../../../../global/constants/size.dart';
import '../../../../services/util_methods.dart';
import '../../../../utils/constants/app_asset.dart';
import '../../../../utils/constants/routes_constant.dart';
import '../../../../utils/manager/storage_manager.dart';
import '../../add_coach/controller/get_center_batch_bloc/get_center_batch_bloc.dart';
import '../../add_coach/controller/get_center_batch_bloc/get_center_batch_event.dart';
import '../../add_coach/controller/get_center_batch_bloc/get_center_batch_state.dart';
import '../../add_coach/model/get_batches_model.dart';
import '../../add_coach/model/get_center_model.dart';
import '../../add_student/controller/get_data_bloc/get_charges_plan_bloc.dart';
import '../../add_student/controller/get_data_bloc/get_charges_plan_event.dart';
import '../../add_student/controller/get_data_bloc/get_charges_plan_state.dart';
import '../../my_academy/view/plan_and_charge/model/charge_info_model.dart';
import '../controller/get_students_for_fees/get_students_for_fees_bloc.dart';
import '../controller/get_students_for_fees/get_students_for_fees_event.dart';
import '../controller/renew_student/renew_student_bloc.dart';

class PastDuesScreen extends StatefulWidget {
  final bool isForInstallments;

  const PastDuesScreen({super.key, required this.isForInstallments});

  @override
  State<PastDuesScreen> createState() => _PastDuesScreenState();
}

class _PastDuesScreenState extends State<PastDuesScreen>
    with TickerProviderStateMixin {
  TabController? _tabController;
  final ValueNotifier<bool> isSelectMultiple = ValueNotifier(false),
      _isFilterApplied = ValueNotifier(false),
      isAllTimeSelected = ValueNotifier(true),
      isLastMonthSelected = ValueNotifier(false);
  ValueNotifier<String> initialStartDate =
          ValueNotifier(DateFormat('yyyy-MM-dd').format(DateTime(2000))),
      initialEndDate = ValueNotifier(DateFormat('yyyy-MM-dd').format(
          DateTime(DateTime.now().year, DateTime.now().month, 1)
              .subtract(const Duration(days: 1))));
  final TextEditingController _searchController = TextEditingController();
  final ValueNotifier<int> _page = ValueNotifier(1),
      totalAmount = ValueNotifier(0),
      resultAmount = ValueNotifier(0),
      selectedTab = ValueNotifier(0),
      _pastDues = ValueNotifier(0);

  final ScrollController _scrollController = ScrollController();
  List<ChargeDetails> assignCharge = [], selectedCharge = [];
  final List<ValueNotifier<bool>> isChargeSelected =
      List.generate(0, (_) => ValueNotifier<bool>(false));
  List<ValueNotifier<bool>> activeStudentList = [], finalActiveStudentList = [];
  List<Batches> assignBatches = [];
  List<Centers> assignCenters = [];
  List<String> selectedSport = [], selectedType = [];
  List<int> selectedBatches = [], selectedCenters = [];
  List<ValueNotifier<bool>> isCenterSelected =
          List.generate(0, (_) => ValueNotifier<bool>(false)),
      isBatchSelected = List.generate(0, (_) => ValueNotifier<bool>(false)),
      isSportSelected = List.generate(0, (_) => ValueNotifier<bool>(false)),
      isPaymentSelected = List.generate(3, (_) => ValueNotifier<bool>(false));

  ValueNotifier<bool> gst = ValueNotifier(false);

  getData() async {
    bool? hasGst =
        await StorageManager().getBoolData(AppConstant.storageConstant.hasGst);
    if (hasGst != null) {
      gst.value = hasGst;
    }
    List<String>? sportList = await StorageManager.instance
        .getList(AppConstant.storageConstant.sports);
    if (sportList != null) {
      isSportSelected.addAll(
        List.generate(sportList.length, (_) => ValueNotifier<bool>(false)),
      );
    }
  }

  void loadStudentsForFees({
    bool shouldReload = true,
    bool isForSearch = false,
    String centerIds = '',
    String sports = '',
    String batchIds = '',
    String type = '',
    required String startDate,
    required String endDate,
  }) {
    context.read<GetStudentsForFeesBloc>().add(GetStudents(
        searchString: _searchController.text,
        page: _page.value,
        isForSearch: isForSearch,
        shouldReload: shouldReload,
        centerIds: centerIds,
        sports: sports,
        type: type,
        batchIds: batchIds,
        coachIds: '',
        studentWithInstallment: 0,
        startDate: startDate,
        endDate: endDate));
  }

  void loadStudentsForTransactions(
      {bool shouldReload = true,
      bool isForSearch = false,
      String centerIds = '',
      String sports = '',
      String batchIds = '',
      String status = 'InProgress'}) {
    context.read<GetStudentsForFeesBloc>().add(GetStudentsForTransaction(
        searchString: _searchController.text,
        page: _page.value,
        isForSearch: isForSearch,
        type: '',
        status: status,
        shouldReload: shouldReload,
        centerIds: centerIds,
        sports: sports,
        batchIds: batchIds,
        coachIds: '',
        startDate: '',
        endDate: ''));
  }

  @override
  void initState() {
    getData();
    context.read<GetChargesPlanBloc>().add(GetAllCharges());
    context.read<GetCenterBatchBloc>().add(GetCenters());
    _tabController = TabController(length: 2, vsync: this, initialIndex: 0);

    if (widget.isForInstallments) {
      loadStudentsForTransactions();
    } else {
      loadStudentsForFees(
          startDate: initialStartDate.value,
          endDate: initialEndDate.value,
          type: selectedType.join(','));
    }
    _scrollController.addListener(
      () => UtilMethods().onScrollListener(() {
        _page.value++;
        if (widget.isForInstallments) {
          loadStudentsForTransactions(
              shouldReload: false,
              centerIds: selectedCenters.join(','),
              sports: selectedSport.join(','),
              status: selectedType.join(','),
              batchIds: selectedBatches.join(','));
        } else {
          loadStudentsForFees(
              shouldReload: false,
              centerIds: selectedCenters.join(','),
              sports: selectedSport.join(','),
              type: selectedType.join(','),
              batchIds: selectedBatches.join(','),
              startDate: initialStartDate.value,
              endDate: initialEndDate.value);
        }
      }, _scrollController),
    );

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _scrollController.removeListener(
      () => UtilMethods().onScrollListener(() {
        _page.value++;
        if (widget.isForInstallments) {
          loadStudentsForTransactions(shouldReload: false);
        } else {
          loadStudentsForFees(
              shouldReload: false,
              startDate: initialStartDate.value,
              endDate: initialEndDate.value);
        }
      }, _scrollController),
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
          widget.isForInstallments ? "Installment Dues" : "Past Dues"),
      body: MultiBlocListener(
        listeners: [
          BlocListener<GetStudentsForFeesBloc, GetStudentsForFeesState>(
            listener: (BuildContext context, GetStudentsForFeesState state) {
              if (state is GetStudentsForFeesSuccess) {
                totalAmount.value = state.totalAmount;
                _pastDues.value = state.totalCount;
              } else if (state is GetStudentsForTransactionsSuccess) {
                totalAmount.value = state.totalAmount;
                _pastDues.value = state.totalCount;
              }
            },
          ),
          BlocListener<GetCenterBatchBloc, GetCenterBatchState>(
              listener: (BuildContext context, GetCenterBatchState state) {
            if (state is GetCenterSuccess) {
              assignCenters = state.getCenterModel.centers;
              isCenterSelected.addAll(
                List.generate(state.getCenterModel.centers.length,
                    (_) => ValueNotifier<bool>(false)),
              );
            } else if (state is GetBatchSuccess) {
              assignBatches = state.getBatchesModel.batchData.batches;
              isBatchSelected.addAll(
                List.generate(state.getBatchesModel.batchData.batches.length,
                    (_) => ValueNotifier<bool>(false)),
              );
            }
          }),
          BlocListener<GetChargesPlanBloc, GetChargePlanState>(
            listener: (context, state) {
              if (state is GetChargeSuccess) {
                assignCharge = state.allChargesModel.chargeData.charges;
                isChargeSelected.addAll(List.generate(
                    state.allChargesModel.chargeData.charges.length,
                    (_) => ValueNotifier<bool>(false)));
              }
            },
          ),
          BlocListener<GetStudentsForFeesBloc, GetStudentsForFeesState>(
            listener: (context, state) {
              if (state is GetStudentsForFeesSuccess) {
                activeStudentList = List.generate(state.studentsForFees.length,
                    (index) => ValueNotifier<bool>(false));
                finalActiveStudentList.addAll(activeStudentList);
              } else if (state is GetStudentsForTransactionsSuccess) {
                activeStudentList = List.generate(
                    state.studentForTransactions.length,
                    (index) => ValueNotifier<bool>(false));
                finalActiveStudentList.addAll(activeStudentList);
              }
            },
          )
        ],
        child: Column(
          children: [
            Gap(MySize.getScaledSizeHeight(20)),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                children: [
                  ValueListenableBuilder(
                    valueListenable: _pastDues,
                    builder: (context, value, child) => Container(
                      width: MySize.getScaledSizeWidth(108),
                      height: MySize.getScaledSizeHeight(105),
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(widget.isForInstallments
                              ? AppAsset.installmentDuesUpperContainer
                              : AppAsset.pastDuesRed),
                          fit: BoxFit.fill,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(left: 7, top: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 18.0),
                              child: TypoGraphy.text("${_pastDues.value}",
                                  color: ColorConstant.absentDarkColor,
                                  level: 4,
                                  fontWeight: FontWeight.w500),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Gap(MySize.getScaledSizeWidth(4)),
                  ValueListenableBuilder(
                    valueListenable: totalAmount,
                    builder: (context, value, child) => Container(
                      width: MySize.getScaledSizeWidth(260),
                      height: MySize.getScaledSizeHeight(105),
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(widget.isForInstallments
                              ? AppAsset.installmentDuesContainer
                              : AppAsset.pastDuesContainer),
                          fit: BoxFit.fill,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(left: 17, top: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            TypoGraphy.text("Amount Pending",
                                color: ColorConstant.textFieldColor,
                                fontSize: MySize.getScaledSizeHeight(12),
                                fontWeight: FontWeight.w500),
                            Row(
                              children: [
                                CustomSvgPicture(
                                  AppAsset.rupeeSvg,
                                  color: ColorConstant.white,
                                  height: MySize.getScaledSizeHeight(16),
                                ),
                                Gap(MySize.getScaledSizeWidth(3)),
                                TypoGraphy.text("${totalAmount.value}",
                                    color: ColorConstant.white,
                                    level: 4,
                                    fontWeight: FontWeight.w500),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Gap(MySize.getScaledSizeHeight(20)),
            Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: MySize.getScaledSizeWidth(10)),
              child: Row(
                children: [
                  Expanded(
                    child: SearchField(
                      onClearTap: () {
                        _page.value = 1;
                        if (widget.isForInstallments) {
                          loadStudentsForTransactions(
                              shouldReload: false,
                              isForSearch: true,
                              centerIds: selectedCenters.join(','),
                              sports: selectedSport.join(','),
                              status: selectedType.join(','),
                              batchIds: selectedBatches.join(','));
                        } else {
                          loadStudentsForFees(
                              shouldReload: false,
                              isForSearch: true,
                              type: selectedType.join(','),
                              startDate: initialStartDate.value,
                              centerIds: selectedCenters.join(','),
                              sports: selectedSport.join(','),
                              batchIds: selectedBatches.join(','),
                              endDate: initialEndDate.value);
                        }
                      },
                      controller: _searchController,
                      onChanged: (val) {
                        _page.value = 1;
                        if (widget.isForInstallments) {
                          loadStudentsForTransactions(
                              shouldReload: false,
                              isForSearch: true,
                              centerIds: selectedCenters.join(','),
                              sports: selectedSport.join(','),
                              status: selectedType.join(','),
                              batchIds: selectedBatches.join(','));
                        } else {
                          loadStudentsForFees(
                              shouldReload: false,
                              isForSearch: true,
                              type: selectedType.join(','),
                              startDate: initialStartDate.value,
                              centerIds: selectedCenters.join(','),
                              sports: selectedSport.join(','),
                              batchIds: selectedBatches.join(','),
                              endDate: initialEndDate.value);
                        }
                      },
                    ),
                  ),
                  Gap(MySize.getScaledSizeWidth(8)),
                  GestureDetector(
                    onTap: () {
                      if (selectedCenters.isEmpty) {
                        for (int i = 0; i < isCenterSelected.length; i++) {
                          isCenterSelected[i].value = false;
                        }
                      }
                      if (selectedBatches.isEmpty) {
                        for (int i = 0; i < isBatchSelected.length; i++) {
                          isBatchSelected[i].value = false;
                        }
                      }
                      if (selectedSport.isEmpty) {
                        for (int i = 0; i < isSportSelected.length; i++) {
                          isSportSelected[i].value = false;
                        }
                      }
                      if (selectedType.isEmpty) {
                        for (int i = 0; i < isPaymentSelected.length; i++) {
                          isPaymentSelected[i].value = false;
                        }
                      }
                      WoltModalSheet.show(
                        context: context,
                        pageListBuilder: (modalSheetContext) {
                          return [
                            MultipleSelectionBottomSheet.filterBottomSheet(
                              isDurationAvailable: !widget.isForInstallments,
                              context,
                              isForHistory: !widget.isForInstallments,
                              isAllTimeSelected: isAllTimeSelected,
                              isLastMonthSelected: isLastMonthSelected,
                              isPaymentStatusAvailable: true,
                              centerData: List.generate(
                                  assignCenters.length,
                                  (index) => Centers(
                                        id: assignCenters[index].id,
                                        name: assignCenters[index].name,
                                      )),
                              batchData: ValueNotifier(List.generate(
                                  assignBatches.length,
                                  (index) => Batches(
                                        id: assignBatches[index].id,
                                        name: assignBatches[index].name,
                                        startTime: '',
                                        endTime: '',
                                      ))),
                              isCenterSelected: isCenterSelected,
                              isBatchSelected: isBatchSelected,
                              onApply: (List<int> selectedCenterIds,
                                  List<int> selectedBatchIds,
                                  List<String> sports,
                                  List<String> payment,
                                  String startDate,
                                  String endDate,
                                  ValueNotifier<bool> isFilterApplied) {
                                selectedCenters = selectedCenterIds;
                                selectedBatches = selectedBatchIds;
                                selectedSport = sports;
                                selectedType = payment;
                                _isFilterApplied.value = isFilterApplied.value;
                                context
                                    .read<GetCenterBatchBloc>()
                                    .add(GetBatches(id: selectedCenterIds));
                                _page.value = 1;
                                if (widget.isForInstallments) {
                                  context.read<GetStudentsForFeesBloc>().add(
                                      GetStudentsForTransaction(
                                          searchString: _searchController.text,
                                          page: _page.value,
                                          isForSearch: true,
                                          shouldReload: true,
                                          status: payment.join(','),
                                          centerIds:
                                              selectedCenterIds.join(','),
                                          sports: sports.join(','),
                                          batchIds: selectedBatchIds.join(','),
                                          type: '',
                                          coachIds: '',
                                          startDate: '',
                                          endDate: ''));
                                } else {
                                  context.read<GetStudentsForFeesBloc>().add(
                                      GetStudents(
                                          type: payment.join(','),
                                          searchString: _searchController.text,
                                          page: _page.value,
                                          isForSearch: true,
                                          shouldReload: false,
                                          centerIds:
                                              selectedCenterIds.join(','),
                                          sports: sports.join(','),
                                          batchIds: selectedBatchIds.join(','),
                                          coachIds: '',
                                          studentWithInstallment: 0,
                                          startDate: startDate,
                                          endDate: endDate));
                                }
                              },
                              isSportSelected: isSportSelected,
                              selectedCenterIds: selectedCenters,
                              initialBatchData: assignBatches,
                              isPaymentStatusSelected: isPaymentSelected,
                            )
                          ];
                        },
                      );
                    },
                    child: CircleAvatar(
                      backgroundColor: ColorConstant.textFieldColor,
                      radius: 22,
                      child: ValueListenableBuilder(
                        valueListenable: _isFilterApplied,
                        builder: (context, value, child) => CustomSvgPicture(
                          AppAsset.filterAttendanceSvg,
                          color: value
                              ? ColorConstant.signUp
                              : ColorConstant.black,
                        ),
                      ),
                    ),
                  ),
                  Gap(MySize.getScaledSizeWidth(8)),
                  GestureDetector(
                    onTap: () {
                      context.pushNamed(RouteConstant.feesReminderScreen);
                    },
                    child: const CircleAvatar(
                      backgroundColor: ColorConstant.textFieldColor,
                      radius: 22,
                      child: CustomSvgPicture(
                        AppAsset.notificationFees,
                        color: ColorConstant.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Gap(MySize.getScaledSizeHeight(10)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ValueListenableBuilder(
                  valueListenable: _isFilterApplied,
                  builder: (context, value, child) => value
                      ? GestureDetector(
                          onTap: () {
                            selectedCenters.clear();
                            selectedBatches.clear();
                            selectedType.clear();
                            selectedSport.clear();
                            isCenterSelected.clear();
                            isBatchSelected.clear();
                            isSportSelected.clear();
                            isPaymentSelected.clear();
                            isCenterSelected = List.generate(
                              assignCenters.length,
                              (index) => ValueNotifier<bool>(false),
                            );
                            isPaymentSelected = List.generate(
                              2,
                              (index) => ValueNotifier<bool>(false),
                            );
                            getData();
                            isBatchSelected = List.generate(
                              assignBatches.length,
                              (index) => ValueNotifier<bool>(false),
                            );
                            _page.value = 1;
                            if (widget.isForInstallments) {
                              loadStudentsForTransactions(
                                  shouldReload: true,
                                  isForSearch: true,
                                  centerIds: selectedCenters.join(','),
                                  sports: selectedSport.join(','),
                                  status: selectedType.join(','),
                                  batchIds: selectedBatches.join(','));
                            } else {
                              loadStudentsForFees(
                                  endDate: initialEndDate.value,
                                  startDate: initialStartDate.value,
                                  shouldReload: true,
                                  isForSearch: true,
                                  centerIds: selectedCenters.join(','),
                                  sports: selectedSport.join(','),
                                  type: selectedType.join(','),
                                  batchIds: selectedBatches.join(','));
                            }
                            _isFilterApplied.value = false;
                          },
                          child: Padding(
                            padding: EdgeInsets.only(
                                left: MySize.getScaledSizeWidth(8)),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Container(
                                decoration: const BoxDecoration(
                                    color: ColorConstant.gradient1,
                                    gradient: LinearGradient(colors: [
                                      ColorConstant.gradient1,
                                      ColorConstant.gradient2,
                                    ]),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(34))),
                                height: MySize.getScaledSizeHeight(35),
                                width: MySize.getScaledSizeWidth(110),
                                child: Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal:
                                          MySize.getScaledSizeWidth(12)),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      TypoGraphy.text("Reset Filter",
                                          color: ColorConstant.white, level: 1),
                                      CustomSvgPicture(
                                        AppAsset.crossIcon,
                                        color: ColorConstant.white,
                                        height: MySize.getScaledSizeHeight(10),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
                ),
                ValueListenableBuilder(
                  valueListenable: isSelectMultiple,
                  builder: (context, value, child) => GestureDetector(
                      onTap: () {
                        isSelectMultiple.value = true;
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => MultiBlocProvider(
                                providers: [
                                  BlocProvider(
                                    create: (context) =>
                                        GetStudentsForFeesBloc(),
                                  ),
                                  BlocProvider(
                                    create: (context) => RenewStudentBloc(),
                                  )
                                ],
                                child: MultipleSelectStudentScreen(
                                  isForInstallment: widget.isForInstallments,
                                  isForPastDues: !widget.isForInstallments,
                                  onTap: (ids) {
                                    for (int i = 0; i < ids.length; i++) {
                                      activeStudentList[i].value = true;
                                      finalActiveStudentList[i].value = true;
                                    }
                                  },
                                ),
                              ),
                            )).then(
                          (value) {
                            _page.value = 1;
                            isSelectMultiple.value = false;
                          },
                        );
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 10.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            GradientText(
                              gradient: const LinearGradient(colors: [
                                ColorConstant.white,
                                ColorConstant.primaryColor
                              ]),
                              child: TypoGraphy.text("Select Multiple",
                                  fontWeight: FontWeight.w600,
                                  color: ColorConstant.primaryColor,
                                  fontSize: MySize.getScaledSizeHeight(15)),
                            ),
                            Gap(MySize.getScaledSizeWidth(6)),
                            CustomSvgPicture(
                              AppAsset.multipleSelectionStudent,
                              height: MySize.getScaledSizeWidth(15),
                              color: ColorConstant.signUp,
                            )
                          ],
                        ),
                      )),
                ),
              ],
            ),
            Gap(MySize.getScaledSizeHeight(20)),
            BlocBuilder<GetStudentsForFeesBloc, GetStudentsForFeesState>(
              builder: (BuildContext context, GetStudentsForFeesState state) {
                if (state is GetStudentsForFeesSuccess) {
                  return state.studentsForFees.isNotEmpty
                      ? Expanded(
                          child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10.0),
                          child: ListView.separated(
                              controller: _scrollController,
                              itemBuilder: (context, index) {
                                int amount = 0;
                                String planType = '';
                                if (index < state.studentsForFees.length) {
                                  amount = state
                                              .studentsForFees[index]
                                              .studentPlans[0]
                                              .paymentInstallment !=
                                          null
                                      ? state
                                                  .studentsForFees[index]
                                                  .studentPlans[0]
                                                  .paymentInstallment!
                                                  .advance ==
                                              0
                                          ? state
                                              .studentsForFees[index]
                                              .studentPlans[0]
                                              .paymentInstallment!
                                              .installment[0]
                                          : state
                                              .studentsForFees[index]
                                              .studentPlans[0]
                                              .paymentInstallment!
                                              .advance
                                      : state.studentsForFees[index]
                                          .studentPlans[0].amount;
                                  planType = state
                                              .studentsForFees[index]
                                              .studentPlans[0]
                                              .paymentInstallment !=
                                          null
                                      ? state
                                                  .studentsForFees[index]
                                                  .studentPlans[0]
                                                  .paymentInstallment!
                                                  .advance ==
                                              0
                                          ? "Installment"
                                          : "Advance"
                                      : "";
                                }
                                return index >= state.studentsForFees.length &&
                                        !state.hasReachedMax
                                    ? const LoaderHelper(
                                        height: 30,
                                      )
                                    : FeesStdCardHelper(
                                        profileImage: File(state
                                            .studentsForFees[index].profilePic),
                                        isPlanExpired: ValueNotifier(DateTime.now()
                                            .isAfter(DateTime.parse(state
                                                .studentsForFees[index]
                                                .studentPlans[0]
                                                .dueDate))),
                                        dateTime: DateFormat("yyyy-MM-dd").parse(state
                                            .studentsForFees[index]
                                            .studentPlans[0]
                                            .dueDate),
                                        name: state.studentsForFees[index].name,
                                        batchName: state
                                                .studentsForFees[index]
                                                .studentAssignBatches[0]
                                                .academyCenterBatch
                                                ?.name ??
                                            "",
                                        endTime: state
                                                .studentsForFees[index]
                                                .studentAssignBatches[0]
                                                .academyCenterBatch
                                                ?.endTime ??
                                            "",
                                        startTime: state.studentsForFees[index].studentAssignBatches[0].academyCenterBatch?.startTime ?? "",
                                        isUnPaid: ValueNotifier(false),
                                        isForUpcoming: ValueNotifier(false),
                                        onStatusTap: () {
                                          DateTime dueDate = DateTime.parse(
                                              state.studentsForFees[index]
                                                  .studentPlans[0].dueDate);
                                          int additionalMonths = state
                                                      .studentsForFees[index]
                                                      .studentPlans[0]
                                                      .months !=
                                                  0
                                              ? state.studentsForFees[index]
                                                  .studentPlans[0].months
                                              : 0;
                                          int additionalDays = state
                                                      .studentsForFees[index]
                                                      .studentPlans[0]
                                                      .days !=
                                                  0
                                              ? state.studentsForFees[index]
                                                  .studentPlans[0].days
                                              : 0;
                                          DateTime endDate = DateTime(
                                            dueDate.year,
                                            dueDate.month + additionalMonths,
                                            dueDate.day + additionalDays,
                                          );
                                          SingleSelectionBottomSheet.feePopUp(
                                              hasGst: gst.value,
                                              extraCharges: ValueNotifier(state
                                                  .studentsForFees[index]
                                                  .studentPlans[0]
                                                  .charge),
                                              startDate: ValueNotifier(DateFormat("dd/MM/yyyy")
                                                  .format(DateTime.parse(state
                                                      .studentsForFees[index]
                                                      .studentPlans[0]
                                                      .dueDate))),
                                              endDate: ValueNotifier(
                                                  DateFormat("dd/MM/yyyy")
                                                      .format(endDate)),
                                              context,
                                              studentName: state
                                                  .studentsForFees[index].name,
                                              batchName: state
                                                      .studentsForFees[index]
                                                      .studentAssignBatches[0]
                                                      .academyCenterBatch
                                                      ?.name ??
                                                  "",
                                              profilePic:
                                                  File(state.studentsForFees[index].profilePic),
                                              planName: state.studentsForFees[index].studentPlans[0].name,
                                              amount: ValueNotifier(amount),
                                              gst: gst.value ? ValueNotifier((amount * 0.18).round()) : ValueNotifier(0),
                                              planStatus: planType,
                                              totalInstallment: state.studentsForFees[index].studentPlans[0].paymentInstallment != null ? state.studentsForFees[index].studentPlans[0].paymentInstallment!.installment.length : 0,
                                              isChargeSelected: isChargeSelected,
                                              assignCharge: assignCharge,
                                              studentId: state.studentsForFees[index].id,
                                              isForInstallment: false,
                                              transactionId: 0,
                                              installmentDate: DateTime.parse(state.studentsForFees[index].studentPlans[0].createdAt), onTap: () {
                                            activeStudentList[index].value =
                                                true;
                                            finalActiveStudentList[index]
                                                .value = true;
                                          }, installmentNumber: 0, tabController: _tabController!, selectedTab: selectedTab);
                                        },
                                        onTap: () {},
                                        isPaymentDone: ValueNotifier(false),
                                        isRenewDone: finalActiveStudentList[index]);
                              },
                              separatorBuilder: (context, index) =>
                                  Gap(MySize.getScaledSizeHeight(12)),
                              itemCount: state.hasReachedMax
                                  ? state.studentsForFees.length
                                  : state.studentsForFees.length + 1),
                        ))
                      : const CustomSvgPicture(AppAsset.noStudentsForFees);
                } else if (state is GetStudentsForTransactionsSuccess) {
                  return state.studentForTransactions.isNotEmpty
                      ? Expanded(
                          child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10.0),
                          child: ListView.separated(
                              controller: _scrollController,
                              itemBuilder: (context, index) {
                                if (index <
                                    state.studentForTransactions.length) {
                                  resultAmount.value = state
                                      .studentForTransactions[index].amount;
                                  if (state.studentForTransactions[index]
                                          .extraCharges!.isNotEmpty) {
                                    List<int> totalAmounts = state
                                        .studentForTransactions[index]
                                        .extraCharges!
                                        .map((e) =>
                                            e.amount + e.amount * e.tax ~/ 100)
                                        .toList();
                                    int sumOfTotalAmounts = totalAmounts
                                            .isNotEmpty
                                        ? totalAmounts.reduce((a, b) => a + b)
                                        : 0;
                                    resultAmount.value -= sumOfTotalAmounts;
                                  }
                                  if (gst.value) {
                                    resultAmount.value -= int.parse((state
                                                .studentForTransactions[index]
                                                .amount *
                                            0.18)
                                        .round()
                                        .toString());
                                  }
                                }

                                return index >=
                                            state.studentForTransactions
                                                .length &&
                                        !state.hasReachedMax
                                    ? const LoaderHelper(
                                        height: 30,
                                      )
                                    : FeesStdCardHelper(
                                        isRenewDone: ValueNotifier(false),
                                        profileImage: File(state
                                            .studentForTransactions[index]
                                            .student
                                            .profilePic),
                                        isPlanExpired: ValueNotifier(DateTime
                                                .now()
                                            .isAfter(DateTime.parse(state
                                                .studentForTransactions[index]
                                                .student
                                                .studentPlans[0]
                                                .dueDate))),
                                        dateTime: DateFormat("yyyy-MM-dd")
                                            .parse(state
                                                .studentForTransactions[index]
                                                .student
                                                .studentPlans[0]
                                                .dueDate),
                                        name: state
                                            .studentForTransactions[index]
                                            .student
                                            .name,
                                        batchName: state
                                            .studentForTransactions[index]
                                            .student
                                            .studentAssignBatches[0]
                                            .academyCenterBatch
                                            .name,
                                        endTime: state
                                            .studentForTransactions[index]
                                            .student
                                            .studentAssignBatches[0]
                                            .academyCenterBatch
                                            .endTime,
                                        startTime: state
                                            .studentForTransactions[index]
                                            .student
                                            .studentAssignBatches[0]
                                            .academyCenterBatch
                                            .startTime,
                                        isUnPaid: ValueNotifier(true),
                                        isForUpcoming: ValueNotifier(false),
                                        onStatusTap: () {
                                          SingleSelectionBottomSheet.feePopUp(
                                                  extraCharges: ValueNotifier([]),
                                                  startDate: ValueNotifier(
                                                      DateFormat("dd/MM/yyyy").format(
                                                          DateTime.parse(state
                                                              .studentForTransactions[
                                                                  index]
                                                              .student
                                                              .studentPlans[0]
                                                              .dueDate))),
                                                  endDate: ValueNotifier(
                                                      DateFormat("dd/MM/yyyy")
                                                          .format(
                                                              DateTime(DateTime.parse(state.studentForTransactions[index].student.studentPlans[0].dueDate).year, DateTime.parse(state.studentForTransactions[index].student.studentPlans[0].dueDate).month + DateTime.parse(state.studentForTransactions[index].student.studentPlans[0].dueDate).month, DateTime.parse(state.studentForTransactions[index].student.studentPlans[0].dueDate).day + DateTime.parse(state.studentForTransactions[index].student.studentPlans[0].dueDate).day))),
                                                  context,
                                                  studentName: state.studentForTransactions[index].student.name,
                                                  batchName: state.studentForTransactions[index].student.studentAssignBatches[0].academyCenterBatch.name,
                                                  profilePic: File(state.studentForTransactions[index].student.profilePic),
                                                  planName: state.studentForTransactions[index].student.studentPlans[0].name,
                                                  amount: resultAmount,
                                                  gst: gst.value ? ValueNotifier(int.parse((state.studentForTransactions[index].amount * 0.18).round().toString())) : ValueNotifier(0),
                                                  planStatus: state.studentForTransactions[index].type,
                                                  totalInstallment: state.studentForTransactions[index].student.studentPlans[0].paymentInstallment?.count ?? 0,
                                                  isChargeSelected: isChargeSelected,
                                                  assignCharge: assignCharge,
                                                  studentId: state.studentForTransactions[index].student.id,
                                                  isForInstallment: true,
                                                  transactionId: state.studentForTransactions[index].id,
                                                  installmentDate: DateTime.now(), onTap: () {
                                            activeStudentList[index].value =
                                                true;
                                            finalActiveStudentList[index]
                                                .value = true;
                                          }, installmentNumber: state.studentForTransactions[index].name.split(' ').last == "Advance" ? 1 : int.parse(state.studentForTransactions[index].name.split(' ').last), tabController: _tabController!, selectedTab: selectedTab, hasGst: gst.value)
                                              .then(
                                            (value) {
                                              _page.value = 1;
                                              loadStudentsForTransactions(
                                                isForSearch: true,
                                                batchIds:
                                                    selectedBatches.join(','),
                                                centerIds:
                                                    selectedCenters.join(','),
                                                sports: selectedSport.join(','),
                                              );
                                            },
                                          );
                                        },
                                        onTap: () {
                                          context.pushNamed(
                                              RouteConstant.userProfileScreen,
                                              args: {
                                                "stdId": state
                                                    .studentForTransactions[
                                                        index]
                                                    .student
                                                    .id,
                                                "profileImage": File(state
                                                    .studentForTransactions[
                                                        index]
                                                    .student
                                                    .profilePic),
                                                "studentName": state
                                                    .studentForTransactions[
                                                        index]
                                                    .student
                                                    .name,
                                                "batchName": state
                                                    .studentForTransactions[
                                                        index]
                                                    .student
                                                    .studentAssignBatches[0]
                                                    .academyCenterBatch
                                                    .name,
                                                "startTime": state
                                                    .studentForTransactions[
                                                        index]
                                                    .student
                                                    .studentAssignBatches[0]
                                                    .academyCenterBatch
                                                    .startTime,
                                                "endTime": state
                                                    .studentForTransactions[
                                                        index]
                                                    .student
                                                    .studentAssignBatches[0]
                                                    .academyCenterBatch
                                                    .endTime,
                                              });
                                        },
                                        isPaymentDone:
                                            finalActiveStudentList[index],
                                      );
                              },
                              separatorBuilder: (context, index) =>
                                  Gap(MySize.getScaledSizeHeight(12)),
                              itemCount: state.hasReachedMax
                                  ? state.studentForTransactions.length
                                  : state.studentForTransactions.length + 1),
                        ))
                      : const CustomSvgPicture(AppAsset.noStudentsForFees);
                } else if (state is GetStudentsForFeesFailure) {
                  return FailureWidget(msg: state.msg);
                }
                return const LoaderHelper();
              },
            )
          ],
        ),
      ),
    );
  }
}
