import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:khelnet/common_widgets/dialog/loader/custom_dialog.dart';
import 'package:khelnet/common_widgets/scrollbar_helper.dart';
import 'package:khelnet/common_widgets/single_selection_bottom_sheet.dart';
import 'package:khelnet/common_widgets/toast/toast_utils.dart';
import 'package:khelnet/utils/extension/navigation_extension.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';

import '../global/constants/app_constant.dart';
import '../global/constants/gradient_text.dart';
import '../global/constants/size.dart';
import '../modules/private_route/add_coach/controller/get_center_batch_bloc/get_center_batch_bloc.dart';
import '../modules/private_route/add_coach/controller/get_center_batch_bloc/get_center_batch_event.dart';
import '../modules/private_route/add_coach/controller/get_center_batch_bloc/get_center_batch_state.dart';
import '../modules/private_route/add_coach/model/get_batches_model.dart';
import '../modules/private_route/add_coach/model/get_center_model.dart';
import '../modules/private_route/add_coach/view/widgets/assign_center_card_helper.dart';
import '../modules/private_route/add_plans/model/plans/add_plan_model.dart';
import '../modules/private_route/add_student/model/student_model.dart';
import '../modules/private_route/add_student/view/widget/student_charge.dart';
import '../modules/private_route/fees/controller/renew_student/renew_student_bloc.dart';
import '../modules/private_route/fees/controller/renew_student/renew_student_event.dart';
import '../modules/private_route/fees/controller/renew_student/renew_student_state.dart';
import '../modules/private_route/fees/model/student_renew_model.dart';
import '../modules/private_route/my_academy/view/plan_and_charge/model/charge_info_model.dart';
import '../modules/private_route/my_academy/view/student/model/std_installment_model.dart';
import '../utils/constants/app_asset.dart';
import '../utils/constants/color_constant.dart';
import '../utils/manager/storage_manager.dart';
import '../utils/theme/typography.dart';
import 'buttons/app_elevated_button.dart';
import 'checkbox_row_widget.dart';
import 'custom_bottom_sheet_container.dart';
import 'custom_svg_picture.dart';
import 'outlined_text_form_field.dart';

class MultipleSelectionBottomSheet {
  static SliverWoltModalSheetPage addSports(
      BuildContext context, Function(List<String>) onSportsSelected,
      {required ValueNotifier<bool> isAnySelected,
      required bool isForBatch,
      bool isForEnquiry = false,
      required List<String> initialSport,
      required ValueNotifier<List<SportModel>> data,
      required TextEditingController customController}) {
    log(initialSport.toString());
    for (var element in data.value) {
      if (!initialSport.contains(element.title)) {
        element.isSelected = false;
      } else {
        element.isSelected = true;
      }
    }
    List<String> selectedSports = [];
    void updateSelectedSports(String sport, int index) {
      if (isForBatch) {
        /// **For batch selection mode: Only one sport can be selected**
        for (var element in data.value) {
          element.isSelected = false; // Unselect all
        }
        data.value[index].isSelected = true; // Select the current sport
      } else {
        /// **If the selected sport is Gymnastics**
        if (sport == "Gymnastics") {
          for (var element in data.value) {
            element.isSelected = false; // Unselect all sports
          }
          data.value[index].isSelected = true; // Select Gymnastics
        } else {
          /// **If Gymnastics is already selected, unselect it**
          for (var element in data.value) {
            if (element.title == "Gymnastics" && element.isSelected) {
              element.isSelected = false;
            }
          }

          /// **Toggle the selected sport**
          data.value[index].isSelected = !data.value[index].isSelected;
        }
      }

      isAnySelected.value = data.value.any((element) => element.isSelected);
      data.notifyListeners();
    }

    return WoltModalSheetPage(
        stickyActionBar: ValueListenableBuilder(
          valueListenable: isAnySelected,
          builder: (context, value, child) => Align(
            alignment: Alignment.center,
            child: AppElevatedButton(
              gradientColor1: value
                  ? ColorConstant.gradient1
                  : ColorConstant.gradient1.withOpacity(0.2),
              gradientColor2: value
                  ? ColorConstant.gradient2
                  : ColorConstant.gradient2.withOpacity(0.5),
              width: MySize.getScaledSizeWidth(320),
              TypoGraphy.text("Done",
                  level: 2,
                  color: ColorConstant.white,
                  fontWeight: FontWeight.w700),
              onPressed: value
                  ? () {
                      Navigator.pop(context, selectedSports);
                      for (var element in data.value) {
                        if (element.isSelected) {
                          selectedSports.add(element.title);
                        }
                      }
                      // selectedSports.addAll(isSelected.entries
                      //     .where((entry) => entry.value.value)
                      //     .map((entry) => entry.key));
                      // selectedSports.forEach(
                      //   (element) {
                      //     if (!isSelected.keys.contains(element)) {
                      //       isSelected[element] = ValueNotifier(true);
                      //     }
                      //   },
                      // );
                      onSportsSelected(selectedSports);
                    }
                  : () {},
            ),
          ),
        ),
        child: ValueListenableBuilder(
          valueListenable: data,
          builder: (context, value, child) => Padding(
            padding:
                EdgeInsets.symmetric(horizontal: MySize.getScaledSizeWidth(8)),
            child: Column(
              children: [
                ...List.generate(
                  (data.value.length / 2).ceil(),
                  // Ensure we don't exceed available elements
                  (index) {
                    int firstIndex = index * 2;
                    int secondIndex = firstIndex + 1;

                    return Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: SportContainer(
                              isCustom: data.value[firstIndex].isCustom,
                              editTap: () {
                                SingleSelectionBottomSheet.addCustomSport(
                                  context,
                                  initialSession: data.value[firstIndex].title,
                                  sessionController: customController,
                                  onTap: (newTitle) {
                                    data.value[firstIndex] = SportModel(
                                      title: newTitle,
                                      isCustom: true,
                                      isSelected: true,
                                      image: AppAsset.customSportWhite,
                                      selectedImage: AppAsset.customSport,
                                    );

                                    customController.clear();

                                    data.notifyListeners();
                                  },
                                );
                              },
                              title: data.value[firstIndex].title,
                              selectedImage:
                                  data.value[firstIndex].selectedImage,
                              image: data.value[firstIndex].image,
                              isSelected: ValueNotifier(
                                  data.value[firstIndex].isSelected),
                              onTap: () {
                                updateSelectedSports(
                                    data.value[firstIndex].title, firstIndex);
                              },
                            ),
                          ),
                        ),

                        // Only add second sport if it exists
                        if (secondIndex < data.value.length)
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: SportContainer(
                                isCustom: data.value[secondIndex].isCustom,
                                editTap: () {
                                  SingleSelectionBottomSheet.addCustomSport(
                                    context,
                                    initialSession:
                                        data.value[secondIndex].title,
                                    sessionController: customController,
                                    onTap: (newTitle) {
                                      data.value[secondIndex] = SportModel(
                                        title: newTitle,
                                        isCustom: true,
                                        isSelected: true,
                                        image: AppAsset.customSportWhite,
                                        selectedImage: AppAsset.customSport,
                                      );

                                      customController.clear();

                                      data.notifyListeners();
                                    },
                                  );
                                },
                                title: data.value[secondIndex].title,
                                selectedImage:
                                    data.value[secondIndex].selectedImage,
                                image: data.value[secondIndex].image,
                                isSelected: ValueNotifier(
                                    data.value[secondIndex].isSelected),
                                onTap: () {
                                  updateSelectedSports(
                                      data.value[secondIndex].title,
                                      secondIndex);
                                },
                              ),
                            ),
                          )
                      ],
                    );
                  },
                ),
                // if (!isForBatch && !isForEnquiry)
                //   Padding(
                //     padding:
                //         EdgeInsets.only(top: MySize.getScaledSizeHeight(10)),
                //     child: GestureDetector(
                //       onTap: () {
                //         SingleSelectionBottomSheet.addCustomSport(context,
                //             onTap: (selection) {
                //               for (var element in data.value) {
                //                 if (element.title == "Gymnastics" && element.isSelected) {
                //                   element.isSelected = false;
                //                 }
                //               }
                //             data.value.add(SportModel(
                //               title: selection,
                //               isCustom: true,
                //               isSelected: true,
                //               image: AppAsset.customSportWhite,
                //               selectedImage: AppAsset.customSport,
                //             ));
                //             isAnySelected.value = true;
                //             data.notifyListeners();
                //             customController.clear();
                //         }, sessionController: TextEditingController());
                //       },
                //       child: GradientText(
                //         gradient: const LinearGradient(colors: [
                //           ColorConstant.white,
                //           ColorConstant.primaryColor,
                //         ]),
                //         child: TypoGraphy.text("+ Add Custom Sport",
                //             level: 2, color: ColorConstant.primaryColor),
                //       ),
                //     ),
                //   ),
                // Padding(
                //   padding: const EdgeInsets.all(4.0),
                //   child: SportContainer(
                //     title: "Custom",
                //     selectedImage: AppAsset.customSport,
                //     image: AppAsset.customSportWhite,
                //     isSelected: ValueNotifier(false),
                //   ),
                // ),
                Gap(MySize.getScaledSizeHeight(80))
              ],
            ),
          ),
        ));
  }

  static Future<void> assignBatch(
    BuildContext context, {
    required Map<Batches, ValueNotifier<bool>> isSelectedList,
    required Function(List<Batches>) onTap,
    required bool isSelectAllBatches,
    required List<Batches> batches,
    bool isForReport = false,
    bool isForFee = false,
    required ValueNotifier<bool> isSelectAll,
    required bool isCenterSelected,
  }) async {
    isSelectAll.value = isSelectAllBatches;
    if (isSelectAll.value) {
      isSelectedList.forEach((center, notifier) {
        notifier.value = true;
      });
    }

    final ScrollController scrollController =
        ScrollController(initialScrollOffset: 1);
    List<Batches> selectedBatches = [];

    showModalBottomSheet(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(10),
        ),
      ),
      backgroundColor: ColorConstant.white,
      context: context,
      builder: (context) {
        return SizedBox(
          height: MySize.getScaledSizeHeight(550),
          child: Column(
            children: [
              Gap(MySize.getScaledSizeHeight(30)),
              TypoGraphy.text(
                  isForFee || isForReport ? "Select Batches" : "Assign Batches",
                  level: 2,
                  fontWeight: FontWeight.w700),
              if (isCenterSelected) Gap(MySize.getScaledSizeHeight(31)),
              if (isCenterSelected && isForReport)
                ValueListenableBuilder(
                  valueListenable: isSelectAll,
                  builder: (context, value, child) => Align(
                    alignment: Alignment.topRight,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 10.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 15.0),
                            child: TypoGraphy.text("Select All",
                                level: 1, fontWeight: FontWeight.w400),
                          ),
                          Gap(MySize.getScaledSizeWidth(10)),
                          Padding(
                            padding: const EdgeInsets.only(right: 10.0),
                            child: Checkbox(
                              activeColor: ColorConstant.signUp,
                              value: isSelectAll.value,
                              onChanged: (value) {
                                isSelectAll.value = value!;
                                isSelectedList.forEach((center, notifier) {
                                  notifier.value = value;
                                });
                              },
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              Expanded(
                child: isCenterSelected
                    ? batches.isNotEmpty
                        ? Padding(
                            padding: const EdgeInsets.only(right: 10.0),
                            child: ScrollbarHelper(
                              scrollController: scrollController,
                              child: ListView.separated(
                                controller: scrollController,
                                itemCount: batches.length,
                                itemBuilder: (context, index) {
                                  return isForReport
                                      ? ValueListenableBuilder(
                                          valueListenable: isSelectedList.values
                                              .elementAt(index),
                                          builder: (context, value, child) {
                                            return CheckBoxWidget(
                                              title: batches[index].name,
                                              isSelected: isSelectedList.values
                                                  .elementAt(index),
                                              onChange: () {
                                                isSelectedList.values
                                                    .elementAt(index)
                                                    .value = !value;
                                                isSelectAll.value =
                                                    isSelectedList.values.every(
                                                        (notifier) =>
                                                            notifier.value);
                                              },
                                            );
                                          },
                                        )
                                      : Padding(
                                          padding:
                                              const EdgeInsets.only(top: 8.0),
                                          child: AssignCenterCardHelper(
                                            bottomLeft: index ==
                                                    batches
                                                        .indexOf(batches.last)
                                                ? 15
                                                : 0,
                                            bottomRight: index ==
                                                    batches
                                                        .indexOf(batches.last)
                                                ? 15
                                                : 0,
                                            topRight: index ==
                                                    batches
                                                        .indexOf(batches.first)
                                                ? 15
                                                : 0,
                                            topLeft: index ==
                                                    batches
                                                        .indexOf(batches.first)
                                                ? 15
                                                : 0,
                                            isSelected: isSelectedList.values
                                                .elementAt(index),
                                            batchName: batches[index].name,
                                            batchTime:
                                                "(${batches[index].startTime} - ${batches[index].endTime} )",
                                            isForCenter: false,
                                          ),
                                        );
                                },
                                separatorBuilder:
                                    (BuildContext context, int index) {
                                  return SizedBox(
                                      height: MySize.getScaledSizeHeight(4));
                                },
                              ),
                            ),
                          )
                        : Center(
                            child: TypoGraphy.text("There is no batch",
                                level: 2, color: ColorConstant.primaryColor),
                          )
                    : Center(
                        child: TypoGraphy.text("Select Center First",
                            level: 2, color: ColorConstant.primaryColor),
                      ),
              ),
              Gap(MySize.getScaledSizeHeight(35)),
              Padding(
                padding: const EdgeInsets.only(bottom: 19.0),
                child: isForReport
                    ? Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                isSelectedList.forEach((center, notifier) {
                                  notifier.value = false;
                                });
                                selectedBatches = [];
                                onTap(selectedBatches);
                                context.pop();
                              },
                              child: TypoGraphy.text(
                                  isForFee ? "Cancel" : "Reset",
                                  level: 3,
                                  fontWeight: FontWeight.w500),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: AppElevatedButton(
                              TypoGraphy.text(
                                  isForFee ? "Select" : "Apply Filter",
                                  level: 3,
                                  fontWeight: FontWeight.w600,
                                  color: ColorConstant.white),
                              onPressed: () {
                                for (int i = 0;
                                    i < isSelectedList.length;
                                    i++) {
                                  if (isSelectedList.values
                                      .elementAt(i)
                                      .value) {
                                    selectedBatches.add(Batches(
                                      id: batches[i].id,
                                      name: batches[i].name,
                                      startTime: batches[i].startTime,
                                      endTime: batches[i].endTime,
                                    ));
                                    log(selectedBatches.toString());
                                  }
                                }
                                onTap(selectedBatches);
                                context.pop();
                              },
                            ),
                          ),
                        ],
                      )
                    : AppElevatedButton(
                        gradientColor1: ColorConstant.gradient1,
                        gradientColor2: ColorConstant.gradient2,
                        height: MySize.getScaledSizeHeight(53),
                        width: MySize.getScaledSizeWidth(320),
                        TypoGraphy.text("Done",
                            color: ColorConstant.white, level: 3),
                        onPressed: () {
                          for (int i = 0; i < isSelectedList.length; i++) {
                            if (isSelectedList.values.elementAt(i).value) {
                              selectedBatches.add(Batches(
                                id: batches[i].id,
                                name: batches[i].name,
                                startTime: batches[i].startTime,
                                endTime: batches[i].endTime,
                              ));
                              log(selectedBatches.toString());
                            }
                          }
                          onTap(selectedBatches);
                          context.pop();
                        },
                      ),
              )
            ],
          ),
        );
      },
    );
  }

  static Future<void> assignCenter(
    BuildContext context, {
    required Map<Centers, ValueNotifier<bool>> isSelectedList,
    required Function(List<Centers>) onTap,
    bool isForReport = false,
    bool isForFees = false,
    bool isForExpense = false,
    required ValueNotifier<bool> isSelectAll,
    required List<Centers> centers,
  }) async {
    List<Centers> selectedCenters = [];
    final ScrollController scrollController =
        ScrollController(initialScrollOffset: 1);
    showModalBottomSheet(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(10),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      backgroundColor: ColorConstant.white,
      context: context,
      builder: (context) {
        return SizedBox(
          height: MySize.getScaledSizeHeight(470),
          child: Column(
            children: [
              Gap(MySize.getScaledSizeHeight(30)),
              TypoGraphy.text(
                  isForFees || isForReport ? "Select Center" : "Assign Center",
                  level: 2,
                  fontWeight: FontWeight.w700),
              Gap(MySize.getScaledSizeHeight(31)),
              if (isForReport)
                ValueListenableBuilder(
                  valueListenable: isSelectAll,
                  builder: (context, value, child) => Align(
                    alignment: Alignment.topRight,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 10.0),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 15.0),
                            child: TypoGraphy.text("Select All",
                                level: 1, fontWeight: FontWeight.w400),
                          ),
                          Gap(
                            MySize.getScaledSizeWidth(10),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(right: 10.0),
                            child: Checkbox(
                              activeColor: ColorConstant.signUp,
                              value: isSelectAll.value,
                              onChanged: (value) {
                                isSelectAll.value = !isSelectAll.value;
                                if (isSelectAll.value) {
                                  isSelectedList.forEach((center, notifier) {
                                    notifier.value = true;
                                  });
                                } else {
                                  isSelectedList.forEach((center, notifier) {
                                    notifier.value = false;
                                  });
                                }
                              },
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              Expanded(
                child: centers.isNotEmpty
                    ? Padding(
                        padding: const EdgeInsets.only(right: 10.0),
                        child: ScrollbarHelper(
                          scrollController: scrollController,
                          child: ListView.separated(
                            controller: scrollController,
                            itemCount: centers.length,
                            itemBuilder: (context, index) {
                              log(centers[index].toString());
                              log(isSelectedList.values
                                  .elementAt(index)
                                  .toString());

                              return isForReport
                                  ? CheckBoxWidget(
                                      title: centers[index].name,
                                      isSelected: isSelectedList.values
                                          .elementAt(index),
                                      onChange: () {
                                        isSelectedList.values
                                                .elementAt(index)
                                                .value =
                                            !isSelectedList.values
                                                .elementAt(index)
                                                .value;
                                      },
                                    )
                                  : AssignCenterCardHelper(
                                      isForCenter: true,
                                      bottomLeft:
                                          index == centers.indexOf(centers.last)
                                              ? 15
                                              : 0,
                                      bottomRight:
                                          index == centers.indexOf(centers.last)
                                              ? 15
                                              : 0,
                                      topRight: index ==
                                              centers.indexOf(centers.first)
                                          ? 15
                                          : 0,
                                      topLeft: index ==
                                              centers.indexOf(centers.first)
                                          ? 15
                                          : 0,
                                      isSelected: isSelectedList.values
                                          .elementAt(index),
                                      batchName: centers[index].name,
                                      batchTime: "",
                                    );
                            },
                            separatorBuilder:
                                (BuildContext context, int index) {
                              return SizedBox(
                                height: MySize.getScaledSizeHeight(4),
                              );
                            },
                          ),
                        ),
                      )
                    : const Center(child: Text("No Center Available")),
              ),
              Gap(MySize.getScaledSizeHeight(35)),
              centers.isNotEmpty
                  ? Padding(
                      padding: const EdgeInsets.only(bottom: 19.0),
                      child: isForReport
                          ? Row(
                              children: [
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () {
                                      isSelectedList
                                          .forEach((center, notifier) {
                                        notifier.value = false;
                                      });
                                      selectedCenters = [];
                                      onTap(selectedCenters);
                                      context.pop();
                                    },
                                    child: TypoGraphy.text(
                                        isForFees || isForExpense
                                            ? "Cancel"
                                            : "Reset",
                                        level: 3,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                    child: AppElevatedButton(
                                      TypoGraphy.text(
                                          isForFees || isForExpense
                                              ? "Select"
                                              : "Apply Filter",
                                          level: 3,
                                          fontWeight: FontWeight.w600,
                                          color: ColorConstant.white),
                                      onPressed: () {
                                        for (int i = 0;
                                            i < centers.length;
                                            i++) {
                                          if (isSelectedList.values
                                              .elementAt(i)
                                              .value) {
                                            selectedCenters.add(centers[i]);
                                            log(selectedCenters.toString());
                                          }
                                        }
                                        onTap(selectedCenters);
                                        context.pop();
                                      },
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : AppElevatedButton(
                              gradientColor1: ColorConstant.gradient1,
                              gradientColor2: ColorConstant.gradient2,
                              height: MySize.getScaledSizeHeight(53),
                              width: MySize.getScaledSizeWidth(320),
                              TypoGraphy.text("Done",
                                  color: ColorConstant.white, level: 3),
                              onPressed: () {
                                for (int i = 0; i < centers.length; i++) {
                                  if (isSelectedList.values
                                      .elementAt(i)
                                      .value) {
                                    selectedCenters.add(centers[i]);
                                    log(selectedCenters.toString());
                                  }
                                }
                                onTap(selectedCenters);
                                context.pop();
                              },
                            ),
                    )
                  : const SizedBox.shrink()
            ],
          ),
        );
      },
    );
  }

  // static Future<void> daySelection(
  //   BuildContext context, {
  //   required Function(List<String>) onSportsSelected,
  //   required List<String> initialSelectedDays,
  // }) async {
  //   List<String> selectedDays = [];
  //   final Map<String, ValueNotifier<bool>> daysSelectionMap = {
  //     "Sunday": ValueNotifier<bool>(initialSelectedDays.contains("Sunday")),
  //     "Monday": ValueNotifier<bool>(initialSelectedDays.contains("Monday")),
  //     "Tuesday": ValueNotifier<bool>(initialSelectedDays.contains("Tuesday")),
  //     "Wednesday":
  //         ValueNotifier<bool>(initialSelectedDays.contains("Wednesday")),
  //     "Thursday": ValueNotifier<bool>(initialSelectedDays.contains("Thursday")),
  //     "Friday": ValueNotifier<bool>(initialSelectedDays.contains("Friday")),
  //     "Saturday": ValueNotifier<bool>(initialSelectedDays.contains("Saturday")),
  //   };
  //
  //   // ValueNotifier to track if any day is selected
  //   final isAnySelected = ValueNotifier<bool>(initialSelectedDays.isNotEmpty);
  //
  //   // ValueNotifier to track if all days are selected
  //   final isAllSelected = ValueNotifier<bool>(initialSelectedDays.length == 7);
  //
  //   // Function to update the isAnySelected and isAllSelected values
  //   void updateSelectionState() {
  //     isAnySelected.value =
  //         daysSelectionMap.values.any((notifier) => notifier.value);
  //     isAllSelected.value =
  //         daysSelectionMap.values.every((notifier) => notifier.value);
  //   }
  //
  //   showModalBottomSheet(
  //     backgroundColor: ColorConstant.white,
  //     isScrollControlled: true,
  //     context: context,
  //     builder: (context) {
  //       return CustomBottomSheetContainer(
  //           child: ValueListenableBuilder(
  //         valueListenable: isAllSelected,
  //         builder: (context, value, child) {
  //           return Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               for (var i = 0; i < daysSelectionMap.entries.length; i += 2)
  //                 Padding(
  //                   padding: const EdgeInsets.only(bottom: 7),
  //                   child: Row(
  //                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                     children: [
  //                       Expanded(
  //                         child: DaysContainer(
  //                           onTap: () {
  //                             daysSelectionMap.entries
  //                                     .elementAt(i)
  //                                     .value
  //                                     .value =
  //                                 !daysSelectionMap.entries
  //                                     .elementAt(i)
  //                                     .value
  //                                     .value;
  //                             updateSelectionState();
  //                           },
  //                           title: daysSelectionMap.entries.elementAt(i).key,
  //                           isSelected:
  //                               daysSelectionMap.entries.elementAt(i).value,
  //                         ),
  //                       ),
  //                       const Gap(7),
  //                       Expanded(
  //                         child: i + 1 < daysSelectionMap.entries.length
  //                             ? DaysContainer(
  //                                 onTap: () {
  //                                   daysSelectionMap.entries
  //                                           .elementAt(i + 1)
  //                                           .value
  //                                           .value =
  //                                       !daysSelectionMap.entries
  //                                           .elementAt(i + 1)
  //                                           .value
  //                                           .value;
  //                                   updateSelectionState();
  //                                 },
  //                                 title: daysSelectionMap.entries
  //                                     .elementAt(i + 1)
  //                                     .key,
  //                                 isSelected: daysSelectionMap.entries
  //                                     .elementAt(i + 1)
  //                                     .value,
  //                               )
  //                             : const SizedBox.shrink(),
  //                       ),
  //                     ],
  //                   ),
  //                 ),
  //               Gap(MySize.getScaledSizeHeight(15)),
  //               GestureDetector(
  //                 onTap: () {
  //                   final newValue = !isAllSelected.value;
  //                   isAllSelected.value = newValue;
  //                   for (var notifier in daysSelectionMap.values) {
  //                     notifier.value = newValue;
  //                   }
  //                   updateSelectionState();
  //                 },
  //                 child: Row(
  //                     mainAxisAlignment: MainAxisAlignment.start,
  //                     children: [
  //                       ValueListenableBuilder(
  //                         valueListenable: isAllSelected,
  //                         builder: (context, value, child) => Container(
  //                           height: 20,
  //                           width: 50,
  //                           decoration: BoxDecoration(
  //                               shape: BoxShape.circle,
  //                               border:
  //                                   Border.all(color: ColorConstant.signUp)),
  //                           child: value
  //                               ? CustomSvgPicture(
  //                                   AppAsset.checkSvg,
  //                                   height: MySize.getScaledSizeHeight(30),
  //                                   width: MySize.getScaledSizeWidth(30),
  //                                 )
  //                               : const SizedBox.shrink(),
  //                         ),
  //                       ),
  //                       TypoGraphy.text("Select All", level: 2),
  //                     ]),
  //               ),
  //               Gap(MySize.getScaledSizeHeight(25)),
  //               Align(
  //                   alignment: Alignment.center,
  //                   child: ValueListenableBuilder(
  //                     valueListenable: isAnySelected,
  //                     builder: (context, value, child) => AppElevatedButton(
  //                       gradientColor1: value
  //                           ? ColorConstant.gradient1
  //                           : ColorConstant.gradient1.withOpacity(0.2),
  //                       gradientColor2: value
  //                           ? ColorConstant.gradient2
  //                           : ColorConstant.gradient2.withOpacity(0.5),
  //                       width: MySize.getScaledSizeWidth(320),
  //                       TypoGraphy.text(
  //                         "Done",
  //                         level: 2,
  //                         fontWeight: FontWeight.w700,
  //                         color: ColorConstant.white,
  //                       ),
  //                       onPressed: () {
  //                         final selectedDays = daysSelectionMap.entries
  //                             .where((entry) => entry.value.value)
  //                             .map((entry) => entry.key)
  //                             .toList();
  //                         Navigator.pop(context, selectedDays);
  //                         onSportsSelected(selectedDays);
  //                       },
  //                     ),
  //                   ))
  //             ],
  //           );
  //         },
  //       ));
  //     },
  //   );
  // }
  static Future<void> daySelection(
    BuildContext context, {
    required Function(List<String>) onSportsSelected,
    required ValueNotifier<Set<String>> selectedDaysNotifier,
  }) async {
    bool isAnySelected(Set<String> days) => days.isNotEmpty;

    bool isAllSelected(Set<String> days) => days.length == 7;
    final dayNames = [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ];
    showModalBottomSheet(
      backgroundColor: ColorConstant.white,
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return CustomBottomSheetContainer(
          child: ValueListenableBuilder<Set<String>>(
            valueListenable: selectedDaysNotifier,
            builder: (context, selectedDays, child) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (int index = 0; index < dayNames.length / 2; index++)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                                vertical: MySize.size5 ?? 5,
                                horizontal: MySize.size5 ?? 5),
                            child: DaysContainer(
                              onTap: () {
                                if (selectedDays
                                    .contains(dayNames[index * 2])) {
                                  selectedDaysNotifier.value =
                                      Set.from(selectedDays)
                                        ..remove(dayNames[index * 2]);
                                } else {
                                  selectedDaysNotifier.value =
                                      Set.from(selectedDays)
                                        ..add(dayNames[index * 2]);
                                }
                              },
                              title: dayNames[index * 2],
                              isSelected: ValueNotifier(
                                  selectedDays.contains(dayNames[index * 2])),
                            ),
                          ),
                        ),
                        if (index * 2 + 1 < dayNames.length ||
                            dayNames.length % 2 == 0)
                          Expanded(
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: MySize.size5 ?? 5,
                                  horizontal: MySize.size5 ?? 5),
                              child: DaysContainer(
                                onTap: () {
                                  if (selectedDays
                                      .contains(dayNames[index * 2 + 1])) {
                                    selectedDaysNotifier.value =
                                        Set.from(selectedDays)
                                          ..remove(dayNames[index * 2 + 1]);
                                  } else {
                                    selectedDaysNotifier.value =
                                        Set.from(selectedDays)
                                          ..add(dayNames[index * 2 + 1]);
                                  }
                                },
                                title: dayNames[index * 2 + 1],
                                isSelected: ValueNotifier(selectedDays
                                    .contains(dayNames[index * 2 + 1])),
                              ),
                            ),
                          ),
                      ],
                    ),

                  Gap(MySize.getScaledSizeHeight(15)),
                  // Select All button
                  GestureDetector(
                    onTap: () {
                      if (isAllSelected(selectedDays)) {
                        selectedDaysNotifier.value = {};
                      } else {
                        selectedDaysNotifier.value = {
                          "Monday",
                          "Tuesday",
                          "Wednesday",
                          "Thursday",
                          "Friday",
                          "Saturday",
                          "Sunday",
                        };
                      }
                    },
                    child: Row(
                      children: [
                        Container(
                          height: MySize.getScaledSizeHeight(20),
                          width: MySize.getScaledSizeWidth(50),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: ColorConstant.signUp),
                          ),
                          child: isAllSelected(selectedDays)
                              ? CustomSvgPicture(
                                  AppAsset.checkSvg,
                                  height: MySize.getScaledSizeHeight(30),
                                  width: MySize.getScaledSizeWidth(30),
                                )
                              : const SizedBox.shrink(),
                        ),
                        TypoGraphy.text("Select All", level: 2),
                      ],
                    ),
                  ),
                  Gap(MySize.getScaledSizeHeight(25)),
                  Align(
                    alignment: Alignment.center,
                    child: AppElevatedButton(
                      gradientColor1: isAnySelected(selectedDays)
                          ? ColorConstant.gradient1
                          : ColorConstant.gradient1.withOpacity(0.2),
                      gradientColor2: isAnySelected(selectedDays)
                          ? ColorConstant.gradient2
                          : ColorConstant.gradient2.withOpacity(0.5),
                      width: MySize.getScaledSizeWidth(320),
                      TypoGraphy.text(
                        "Done",
                        level: 2,
                        fontWeight: FontWeight.w700,
                        color: ColorConstant.white,
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                        onSportsSelected(selectedDays.toList());
                      },
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  static Future<void> additionalInfo(
    BuildContext context, {
    required TextEditingController nameController,
    required TextEditingController numberController,
    required TextEditingController dateController,
    required TextEditingController addressController,
    required TextEditingController emailController,
    required TextEditingController schoolController,
    required String initialSelectedGender,
    required Function(AdditionalInfo additionalInfo) onTap,
  }) async {
    String selectedGender = initialSelectedGender;

    showModalBottomSheet(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(10),
        ),
      ),
      backgroundColor: ColorConstant.white,
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: SizedBox(
                  height: MySize.getScaledSizeHeight(600),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Gap(MySize.getScaledSizeHeight(26)),
                        RichText(
                            text: TextSpan(children: [
                          TextSpan(
                              text: "Additional Info",
                              style: GoogleFonts.poppins(
                                  textStyle: TextStyle(
                                      fontWeight: FontWeight.w700,
                                      color: ColorConstant.black,
                                      fontSize:
                                          MySize.getScaledSizeHeight(16)))),
                          TextSpan(
                              text: " (Optional)",
                              style: GoogleFonts.poppins(
                                  textStyle: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: ColorConstant.black,
                                      fontSize:
                                          MySize.getScaledSizeHeight(14))))
                        ])),
                        Gap(MySize.getScaledSizeHeight(30)),
                        OutlinedTextFormField(
                          controller: nameController,
                          isOnboard: true,
                          verticalPadding: 4,
                          hintText: "Parent Name",
                        ),
                        OutlinedTextFormField(
                          textInputType: TextInputType.emailAddress,
                          verticalPadding: 4,
                          controller: emailController,
                          isOnboard: true,
                          isEmail: true,
                          hintText: "Email Address",
                        ),
                        OutlinedTextFormField(
                          inputFormatters: [
                            LengthLimitingTextInputFormatter(10)
                          ],
                          isRequired: true,
                          isOnboard: true,
                          validator: (val) {
                            const numRegex = r'^(?!(\d)\1{9})[1-9][0-9]{9}$';
                            const number = r'^[0-9]+$';
                            if (val!.isEmpty || val == '') {
                              return "Please Enter Mobile Number";
                            } else if (val.length < 10) {
                              return "Please Enter Valid Mobile Number";
                            } else if (!RegExp(numRegex).hasMatch(val) ||
                                !RegExp(number).hasMatch(val)) {
                              return "Please Enter Valid Mobile Number";
                            }
                            return null;
                          },
                          textInputType: TextInputType.phone,
                          verticalPadding: 4,
                          controller: numberController,
                          hintText: "Alternate Mobile Number",
                        ),
                        OutlinedTextFormField(
                          onTap: (context) async {
                            DateTime initialDate = DateTime.now();
                            DateTime? pickedDate = await showDatePicker(
                              builder: (context, child) {
                                return Theme(
                                    data: Theme.of(context).copyWith(
                                      // override MaterialApp ThemeData
                                      colorScheme: const ColorScheme.light(
                                        primary: ColorConstant.signUp,
                                        onPrimary: Colors.white,
                                        onSurface:
                                            Colors.black, // Month days , years
                                      ),
                                      textButtonTheme: TextButtonThemeData(
                                        style: TextButton.styleFrom(
                                          foregroundColor: ColorConstant
                                              .signUp, // ok , cancel    buttons
                                        ),
                                      ),
                                    ),
                                    child: child!);
                              },
                              context: context,
                              initialDate: dateController.text.isEmpty
                                  ? initialDate
                                  : DateFormat('dd-MM-yyyy')
                                      .parse(dateController.text),
                              firstDate: DateTime(1800),
                              lastDate: DateTime(2030),
                            );
                            if (pickedDate != null) {
                              setState(() {
                                dateController.text =
                                    DateFormat('dd-MM-yyyy').format(pickedDate);
                                initialDate = pickedDate;
                              });
                            }
                          },
                          verticalPadding: 4,
                          controller: dateController,
                          isOnboard: true,
                          hintText: "DOB",
                        ),
                        OutlinedTextFormField(
                          verticalPadding: 4,
                          controller: addressController,
                          isOnboard: true,
                          hintText: "Address",
                        ),
                        OutlinedTextFormField(
                          verticalPadding: 4,
                          controller: schoolController,
                          isOnboard: true,
                          hintText: "School Name",
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Radio<String>(
                                  activeColor: ColorConstant.signUp,
                                  value: 'Male',
                                  groupValue: selectedGender,
                                  onChanged: (value) {
                                    setState(() {
                                      selectedGender = value!;
                                    });
                                  },
                                ),
                                TypoGraphy.text("Male",
                                    level: 1, fontWeight: FontWeight.w500),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Radio<String>(
                                  activeColor: ColorConstant.signUp,
                                  value: 'Female',
                                  groupValue: selectedGender,
                                  onChanged: (value) {
                                    setState(() {
                                      selectedGender = value!;
                                    });
                                  },
                                ),
                                TypoGraphy.text("Female",
                                    level: 1, fontWeight: FontWeight.w500),
                              ],
                            ),
                          ],
                        ),
                        Gap(MySize.getScaledSizeHeight(30)),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: AppElevatedButton(
                            width: MySize.getScaledSizeWidth(347),
                            TypoGraphy.text("Done",
                                level: 3, color: ColorConstant.white),
                            onPressed: () {
                              log(selectedGender);
                              onTap(AdditionalInfo(
                                  fatherName: nameController.text,
                                  alternateMobile: numberController.text,
                                  dob: dateController.text,
                                  address: addressController.text,
                                  email: emailController.text,
                                  gender: selectedGender,
                                  schoolName: schoolController.text));
                              context.pop();
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  static Future<void> selectCharge(
    BuildContext context, {
    required Map<ChargeDetails, ValueNotifier<bool>> isSelectedList,
    required Function(List<ChargeDetails>) onTap,
    required List<ChargeDetails> charges,
  }) async {
    List<ChargeDetails> selectedCharges = [];
    ScrollController scrollController =
        ScrollController(initialScrollOffset: 1);

    showModalBottomSheet(
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(10),
        ),
      ),
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return SizedBox(
          height: MySize.getScaledSizeHeight(511),
          child: Column(
            children: [
              Gap(MySize.getScaledSizeHeight(26)),
              TypoGraphy.text("Additional Charges",
                  level: 1, fontWeight: FontWeight.w700),
              Gap(MySize.getScaledSizeHeight(26)),
              Expanded(
                child: charges.isNotEmpty
                    ? Padding(
                        padding: EdgeInsets.only(
                            right: MySize.getScaledSizeWidth(8),
                            left: MySize.getScaledSizeWidth(8)),
                        child: ScrollbarHelper(
                          scrollController: scrollController,
                          child: ListView.separated(
                            controller: scrollController,
                            itemCount: charges.length,
                            itemBuilder: (context, index) {
                              return StudentCharge(
                                bottomLeft:
                                    index == charges.indexOf(charges.last)
                                        ? 15
                                        : 0,
                                bottomRight:
                                    index == charges.indexOf(charges.last)
                                        ? 15
                                        : 0,
                                topRight:
                                    index == charges.indexOf(charges.first)
                                        ? 15
                                        : 0,
                                topLeft: index == charges.indexOf(charges.first)
                                    ? 15
                                    : 0,
                                isSelected:
                                    isSelectedList.values.elementAt(index),
                                chargeName: charges[index].name,
                                amount: charges[index].amount.toString(),
                              );
                            },
                            separatorBuilder:
                                (BuildContext context, int index) {
                              return SizedBox(
                                height: MySize.getScaledSizeHeight(4),
                              );
                            },
                          ),
                        ),
                      )
                    : Center(
                        child: TypoGraphy.text("There is no Charges",
                            level: 2, color: ColorConstant.primaryColor),
                      ),
              ),
              Gap(MySize.getScaledSizeHeight(35)),
              Padding(
                padding: const EdgeInsets.only(bottom: 19.0),
                child: AppElevatedButton(
                  gradientColor1: ColorConstant.gradient1,
                  gradientColor2: ColorConstant.gradient2,
                  height: MySize.getScaledSizeHeight(53),
                  width: MySize.getScaledSizeWidth(320),
                  TypoGraphy.text("Done", color: ColorConstant.white, level: 3),
                  onPressed: () {
                    for (int i = 0; i < isSelectedList.length; i++) {
                      if (isSelectedList.values.elementAt(i).value) {
                        selectedCharges.add(ChargeDetails(
                            id: charges[i].id,
                            name: charges[i].name,
                            amount: charges[i].amount,
                            createdAt: charges[i].createdAt,
                            updatedAt: charges[i].updatedAt,
                            deletedAt: charges[i].deletedAt,
                            academyId: charges[i].academyId,
                            tax: charges[i].tax,
                            hsnCode: charges[i].hsnCode));
                        log(selectedCharges.toString());
                      }
                    }
                    onTap(selectedCharges);
                    context.pop();
                  },
                ),
              )
            ],
          ),
        );
      },
    );
  }

  // static Future<void> filter(BuildContext context,
  //     {required List<Centers> centerData,
  //     required ValueNotifier<List<Batches>> batchData,
  //     required List<Batches> initialBatchData,
  //     required List<int> selectedCenterIds,
  //     bool isSportAvailable = true,
  //     bool isBatchAvailable = true,
  //     required void Function(
  //             List<int> selectedCenterIds,
  //             List<int> selectedBatchIds,
  //             List<String> sports,
  //             ValueNotifier<bool> isFilterApplied)
  //         onApply,
  //     required List<ValueNotifier<bool>> isCenterSelected,
  //     required List<ValueNotifier<bool>> isBatchSelected,
  //     required List<ValueNotifier<bool>> isSportSelected}) async {
  //   List<String> sports = [];
  //   batchData.value = initialBatchData;
  //   ValueNotifier<bool> isCenterExpanded = ValueNotifier(true),
  //       isBatchExpanded = ValueNotifier(false),
  //       isSportExpanded = ValueNotifier(false);
  //   Future<void> getData() async {
  //     context.read<GetCenterBatchBloc>().add(GetBatches(id: selectedCenterIds));
  //     List<String>? sportList = await StorageManager.instance
  //         .getList(AppConstant.storageConstant.sports);
  //     if (sportList != null) {
  //       sports = sportList;
  //     }
  //   }
  //
  //   ValueNotifier<double> heightFactor = ValueNotifier(0.5);
  //   await getData();
  //
  //   List<int> ids = [];
  //   List<String> names = [];
  //   for (int i = 0; i < centerData.length; i++) {
  //     if (isCenterSelected[i].value && !ids.contains(centerData[i].id)) {
  //       ids.add(centerData[i].id);
  //       names.add(centerData[i].name);
  //     } else if (!isCenterSelected[i].value && ids.contains(centerData[i].id)) {
  //       ids.remove(centerData[i].id);
  //       names.remove(centerData[i].name);
  //     }
  //   }
  //   ValueNotifier<bool> isAnyCenterSelected = ValueNotifier(false);
  //   ExpansionTileController centerController = ExpansionTileController(),
  //       batchController = ExpansionTileController(),
  //       sportController = ExpansionTileController();
  //
  //   void updateCenterSelection(int index) {
  //     isCenterSelected[index].value = !isCenterSelected[index].value;
  //     for (int i = 0; i < centerData.length; i++) {
  //       if (isCenterSelected[i].value && !ids.contains(centerData[i].id)) {
  //         ids.add(centerData[i].id);
  //         names.add(centerData[i].name);
  //       } else if (!isCenterSelected[i].value &&
  //           ids.contains(centerData[i].id)) {
  //         ids.remove(centerData[i].id);
  //         names.remove(centerData[i].name);
  //       }
  //     }
  //     isAnyCenterSelected.value = ids.isNotEmpty;
  //   }
  //
  //   if (batchData.value.isNotEmpty) {
  //     isAnyCenterSelected.value = true;
  //   }
  //   showModalBottomSheet(
  //     isScrollControlled: true,
  //     backgroundColor: Colors.white,
  //     shape: const RoundedRectangleBorder(
  //       borderRadius: BorderRadius.vertical(
  //         top: Radius.circular(10),
  //       ),
  //     ),
  //     context: context,
  //     builder: (context) {
  //       return BlocProvider(
  //         create: (context) => GetCenterBatchBloc(),
  //         child: Builder(
  //           builder: (context) {
  //             return BlocListener<GetCenterBatchBloc, GetCenterBatchState>(
  //                 listener: (BuildContext context, GetCenterBatchState state) {
  //                   if (state is GetBatchSuccess) {
  //                     if (state.getBatchesModel.batchData.batches.length > 2) {
  //                       heightFactor.value = 0.7;
  //                     }
  //                     batchData.value = state.getBatchesModel.batchData.batches;
  //                     isBatchSelected.clear();
  //                     isBatchSelected.addAll(List.generate(
  //                       state.getBatchesModel.batchData.batches.length,
  //                       (_) => ValueNotifier<bool>(false),
  //                     ));
  //                     if (ids.length > 1) {
  //                       for (var notifier in isBatchSelected) {
  //                         notifier.value = true;
  //                       }
  //                     }
  //                   }
  //                 },
  //                 child: ValueListenableBuilder(
  //                     valueListenable: heightFactor,
  //                     builder: (context, value, child) => FractionallySizedBox(
  //                         heightFactor: heightFactor.value,
  //                         child: Column(
  //                           children: [
  //                             Expanded(
  //                               child: SingleChildScrollView(
  //                                 child: Padding(
  //                                   padding: const EdgeInsets.symmetric(
  //                                       vertical: 16.0),
  //                                   // Adjust padding as needed
  //                                   child: Column(
  //                                     children: [
  //                                       TypoGraphy.text("Filter",
  //                                           level: 3,
  //                                           fontWeight: FontWeight.w600),
  //                                       Gap(MySize.getScaledSizeHeight(12)),
  //                                       const Divider(
  //                                           height: 2,
  //                                           thickness: 2,
  //                                           color:
  //                                               ColorConstant.textFieldColor),
  //                                       ExpansionTile(
  //                                         onExpansionChanged: (value) {
  //                                           isCenterExpanded.value = value;
  //                                           if (isBatchExpanded.value &&
  //                                               isCenterExpanded.value) {
  //                                             heightFactor.value = 0.7;
  //                                           } else if (isCenterExpanded.value) {
  //                                             heightFactor.value = 0.5;
  //                                           }
  //                                         },
  //                                         controller: centerController,
  //                                         iconColor: ColorConstant.signUp,
  //                                         shape: const OutlineInputBorder(
  //                                             borderSide: BorderSide(
  //                                                 color: ColorConstant
  //                                                     .transparent),
  //                                             borderRadius: BorderRadius.all(
  //                                                 Radius.circular(26))),
  //                                         initiallyExpanded: true,
  //                                         backgroundColor:
  //                                             ColorConstant.transparent,
  //                                         title: TypoGraphy.text("Center",
  //                                             level: 1,
  //                                             fontWeight: FontWeight.w400,
  //                                             textAlign: TextAlign.start),
  //                                         children: List.generate(
  //                                           centerData.length,
  //                                           (index) => CheckBoxWidget(
  //                                             onChange: () {
  //                                               updateCenterSelection(index);
  //                                               log(names.toString());
  //                                               log(ids.toString());
  //                                               context
  //                                                   .read<GetCenterBatchBloc>()
  //                                                   .add(GetBatches(id: ids));
  //                                             },
  //                                             title: centerData[index].name,
  //                                             isSelected:
  //                                                 isCenterSelected[index],
  //                                           ),
  //                                         ),
  //                                       ),
  //                                       const Divider(
  //                                           height: 2,
  //                                           thickness: 2,
  //                                           color:
  //                                               ColorConstant.textFieldColor),
  //                                       Gap(MySize.getScaledSizeHeight(8)),
  //                                       const Divider(
  //                                           height: 2,
  //                                           thickness: 2,
  //                                           color:
  //                                               ColorConstant.textFieldColor),
  //                                       if (isSportAvailable)
  //                                         ExpansionTile(
  //                                           onExpansionChanged: (value) {
  //                                             if (value) {
  //                                               isSportExpanded.value = value;
  //                                               centerController.collapse();
  //                                               if (batchData
  //                                                   .value.isNotEmpty) {
  //                                                 batchController.collapse();
  //                                               }
  //                                             }
  //                                             if (isSportExpanded.value &&
  //                                                 isCenterExpanded.value) {
  //                                               heightFactor.value = 0.7;
  //                                             } else if (isSportExpanded
  //                                                 .value) {
  //                                               heightFactor.value = 0.4;
  //                                             }
  //                                           },
  //                                           controller: sportController,
  //                                           iconColor: ColorConstant.signUp,
  //                                           shape: const OutlineInputBorder(
  //                                               borderSide: BorderSide(
  //                                                   color: ColorConstant
  //                                                       .transparent),
  //                                               borderRadius: BorderRadius.all(
  //                                                   Radius.circular(26))),
  //                                           backgroundColor:
  //                                               ColorConstant.transparent,
  //                                           title: TypoGraphy.text("Sports",
  //                                               level: 1,
  //                                               fontWeight: FontWeight.w400,
  //                                               textAlign: TextAlign.start),
  //                                           children: List.generate(
  //                                             sports.length,
  //                                             (index) => CheckBoxWidget(
  //                                               title: sports[index],
  //                                               isSelected:
  //                                                   isSportSelected[index],
  //                                               onChange: () {
  //                                                 isSportSelected[index].value =
  //                                                     !isSportSelected[index]
  //                                                         .value;
  //                                               },
  //                                             ),
  //                                           ),
  //                                         ),
  //                                       if (isSportAvailable)
  //                                         const Divider(
  //                                             height: 2,
  //                                             thickness: 2,
  //                                             color:
  //                                                 ColorConstant.textFieldColor),
  //                                       if (isBatchAvailable)
  //                                         Gap(MySize.getScaledSizeHeight(8)),
  //                                       if (isBatchAvailable)
  //                                         ValueListenableBuilder(
  //                                           valueListenable: batchData,
  //                                           builder:
  //                                               (context, batchData, child) =>
  //                                                   ValueListenableBuilder(
  //                                             valueListenable:
  //                                                 isAnyCenterSelected,
  //                                             builder: (context, value,
  //                                                     child) =>
  //                                                 value && batchData.isNotEmpty
  //                                                     ? const Divider(
  //                                                         height: 2,
  //                                                         thickness: 2,
  //                                                         color: ColorConstant
  //                                                             .textFieldColor)
  //                                                     : const SizedBox.shrink(),
  //                                           ),
  //                                         ),
  //                                       if (isBatchAvailable)
  //                                         ValueListenableBuilder(
  //                                           valueListenable:
  //                                               isAnyCenterSelected,
  //                                           builder: (context, value, child) =>
  //                                               value
  //                                                   ? ExpansionTile(
  //                                                       controller:
  //                                                           batchController,
  //                                                       onExpansionChanged:
  //                                                           (value) {
  //                                                         if (value) {
  //                                                           isBatchExpanded
  //                                                               .value = value;
  //                                                           centerController
  //                                                               .collapse();
  //                                                           sportController
  //                                                               .collapse();
  //                                                         }
  //                                                         if (isBatchExpanded
  //                                                                 .value &&
  //                                                             isCenterExpanded
  //                                                                 .value) {
  //                                                           heightFactor.value =
  //                                                               0.7;
  //                                                         } else if (isBatchExpanded
  //                                                             .value) {
  //                                                           heightFactor.value =
  //                                                               0.5;
  //                                                         }
  //                                                       },
  //                                                       iconColor: ColorConstant
  //                                                           .signUp,
  //                                                       shape: const OutlineInputBorder(
  //                                                           borderSide: BorderSide(
  //                                                               color: ColorConstant
  //                                                                   .transparent),
  //                                                           borderRadius:
  //                                                               BorderRadius
  //                                                                   .all(Radius
  //                                                                       .circular(
  //                                                                           26))),
  //                                                       backgroundColor:
  //                                                           ColorConstant
  //                                                               .transparent,
  //                                                       title: TypoGraphy.text(
  //                                                           "Batches",
  //                                                           level: 1,
  //                                                           fontWeight:
  //                                                               FontWeight.w400,
  //                                                           textAlign: TextAlign
  //                                                               .start),
  //                                                       children: [
  //                                                         ValueListenableBuilder<
  //                                                             List<Batches>>(
  //                                                           valueListenable:
  //                                                               batchData,
  //                                                           builder: (context,
  //                                                               batches,
  //                                                               child) {
  //                                                             return Column(
  //                                                               children: List
  //                                                                   .generate(
  //                                                                 batches
  //                                                                     .length,
  //                                                                 (index) =>
  //                                                                     CheckBoxWidget(
  //                                                                   title: batches[
  //                                                                           index]
  //                                                                       .name,
  //                                                                   isSelected:
  //                                                                       isBatchSelected[
  //                                                                           index],
  //                                                                   onChange:
  //                                                                       () {
  //                                                                     isBatchSelected[
  //                                                                             index]
  //                                                                         .value = !isBatchSelected[
  //                                                                             index]
  //                                                                         .value;
  //                                                                   },
  //                                                                 ),
  //                                                               ),
  //                                                             );
  //                                                           },
  //                                                         ),
  //                                                       ],
  //                                                     )
  //                                                   : const SizedBox.shrink(),
  //                                         ),
  //                                     ],
  //                                   ),
  //                                 ),
  //                               ),
  //                             ),
  //                             const Divider(
  //                               height: 2,
  //                               thickness: 2,
  //                               color: ColorConstant.textFieldColor,
  //                             ),
  //                             Padding(
  //                               padding: const EdgeInsets.all(16.0),
  //                               child: Row(
  //                                 children: [
  //                                   Expanded(
  //                                     child: GestureDetector(
  //                                       onTap: () {
  //                                         for (var item in isCenterSelected) {
  //                                           item.value = false;
  //                                         }
  //                                         for (var item in isBatchSelected) {
  //                                           item.value = false;
  //                                         }
  //
  //                                         for (var item in isSportSelected) {
  //                                           item.value = false;
  //                                         }
  //                                         List<int> selectedBatchIds = [];
  //                                         List<String> selectedSports = [];
  //                                         ValueNotifier<bool> isFilterApplied =
  //                                             ValueNotifier(false);
  //                                         ids.clear();
  //                                         names.clear();
  //                                         selectedBatchIds.clear();
  //                                         onApply(ids, selectedBatchIds,
  //                                             selectedSports, isFilterApplied);
  //                                         context.pop();
  //                                       },
  //                                       child: TypoGraphy.text("Reset",
  //                                           level: 3,
  //                                           fontWeight: FontWeight.w500),
  //                                     ),
  //                                   ),
  //                                   Expanded(
  //                                     flex: 2,
  //                                     child: AppElevatedButton(
  //                                       TypoGraphy.text("Apply Filter",
  //                                           level: 3,
  //                                           fontWeight: FontWeight.w600,
  //                                           color: ColorConstant.white),
  //                                       onPressed: () {
  //                                         List<int> selectedBatchIds = [];
  //                                         List<String> selectedSports = [];
  //                                         ValueNotifier<bool> isFilterApplied =
  //                                             ValueNotifier(selectedBatchIds
  //                                                     .isNotEmpty ||
  //                                                 selectedBatchIds.isNotEmpty ||
  //                                                 ids.isNotEmpty);
  //                                         for (int i = 0;
  //                                             i < isBatchSelected.length;
  //                                             i++) {
  //                                           if (batchData.value.isNotEmpty) {
  //                                             if (isBatchSelected[i].value) {
  //                                               selectedBatchIds
  //                                                   .add(batchData.value[i].id);
  //                                             }
  //                                           }
  //                                         }
  //
  //                                         for (int i = 0;
  //                                             i < isSportSelected.length;
  //                                             i++) {
  //                                           if (isSportSelected[i].value) {
  //                                             if (sports[i] == "Cricket") {
  //                                               selectedSports.add("Cricket");
  //                                             } else if (sports[i] ==
  //                                                 "Football") {
  //                                               selectedSports.add("Football");
  //                                             } else if (sports[i] ==
  //                                                 "Tennis") {
  //                                               selectedSports.add("Tennis");
  //                                             } else if (sports[i] ==
  //                                                 "Badminton") {
  //                                               selectedSports.add("Badminton");
  //                                             } else if (sports[i] ==
  //                                                 "Martial Arts") {
  //                                               selectedSports
  //                                                   .add("Martial Arts");
  //                                             } else if (sports[i] ==
  //                                                 "Archery") {
  //                                               selectedSports.add("Archery");
  //                                             } else if (sports[i] ==
  //                                                 "Pilates") {
  //                                               selectedSports.add("Pilates");
  //                                             } else if (sports[i] ==
  //                                                 "Shooting") {
  //                                               selectedSports.add("Shooting");
  //                                             } else if (sports[i] ==
  //                                                 "Table Tennis") {
  //                                               selectedSports
  //                                                   .add("Table Tennis");
  //                                             } else if (sports[i] ==
  //                                                 "Basketball") {
  //                                               selectedSports
  //                                                   .add("Basketball");
  //                                             } else if (sports[i] ==
  //                                                 "Gymnastics") {
  //                                               selectedSports
  //                                                   .add("Gymnastics");
  //                                             }
  //                                           }
  //                                         }
  //                                         onApply(ids, selectedBatchIds,
  //                                             selectedSports, isFilterApplied);
  //                                         context.pop();
  //                                       },
  //                                     ),
  //                                   ),
  //                                 ],
  //                               ),
  //                             ),
  //                           ],
  //                         ))));
  //           },
  //         ),
  //       );
  //     },
  //   );
  // }

  static SliverWoltModalSheetPage filterBottomSheet(BuildContext context,
      {required List<Centers> centerData,
      required ValueNotifier<List<Batches>> batchData,
      required List<Batches> initialBatchData,
      required List<int> selectedCenterIds,
      bool isSportAvailable = true,
      bool isForCoach = false,
      bool isBatchAvailable = true,
      bool isPaymentStatusAvailable = false,
      bool isForMain = false,
      bool isForHistory = false,
      bool isDurationAvailable = false,
      required void Function(
              List<int> selectedCenterIds,
              List<int> selectedBatchIds,
              List<String> sports,
              List<String> paymentStatus,
              String startDate,
              String endDate,
              ValueNotifier<bool> isFilterApplied)
          onApply,
      required List<ValueNotifier<bool>> isCenterSelected,
      required List<ValueNotifier<bool>> isBatchSelected,
      required List<ValueNotifier<bool>> isPaymentStatusSelected,
      required ValueNotifier<bool> isAllTimeSelected,
      required ValueNotifier<bool> isLastMonthSelected,
      required List<ValueNotifier<bool>> isSportSelected}) {
    ValueNotifier<List<String>> sports = ValueNotifier([]);
    batchData.value = initialBatchData;
    context.read<GetCenterBatchBloc>().add(GetBatches(id: selectedCenterIds));
    Future<void> getData() async {
      List<String>? sportList = await StorageManager.instance
          .getList(AppConstant.storageConstant.sports);
      if (sportList != null) {
        sports.value = sportList;
      }
    }

    List<String> access = [
      "Performance Ratings",
      "Fees",
      "Expenses",
      "Attendance",
      "My Academy",
    ];
    List<String> paymentStatusName = [];
    if (isForMain) {
      paymentStatusName.addAll(["Renewals", "New Admission"]);
    } else if (isForHistory) {
      paymentStatusName.addAll(["Installments", "Extra Charge", "Normal"]);
    } else {
      paymentStatusName.addAll(["Paid", "Unpaid"]);
    }

    ValueNotifier<double> heightFactor = ValueNotifier(0.5);
    getData();

    List<int> ids = [];
    List<String> names = [];
    for (int i = 0; i < centerData.length; i++) {
      if (isCenterSelected[i].value && !ids.contains(centerData[i].id)) {
        ids.add(centerData[i].id);
        names.add(centerData[i].name);
      } else if (!isCenterSelected[i].value && ids.contains(centerData[i].id)) {
        ids.remove(centerData[i].id);
        names.remove(centerData[i].name);
      }
    }
    ValueNotifier<bool> isAnyCenterSelected = ValueNotifier(false);
    ExpansionTileController centerController = ExpansionTileController(),
        batchController = ExpansionTileController(),
        sportController = ExpansionTileController();

    void updateCenterSelection(int index) {
      isCenterSelected[index].value = !isCenterSelected[index].value;
      for (int i = 0; i < centerData.length; i++) {
        if (isCenterSelected[i].value && !ids.contains(centerData[i].id)) {
          ids.add(centerData[i].id);
          names.add(centerData[i].name);
        } else if (!isCenterSelected[i].value &&
            ids.contains(centerData[i].id)) {
          ids.remove(centerData[i].id);
          names.remove(centerData[i].name);
        }
      }
      isAnyCenterSelected.value = ids.isNotEmpty;
    }

    if (batchData.value.isNotEmpty) {
      isAnyCenterSelected.value = true;
    }
    return WoltModalSheetPage(
      hasSabGradient: false,
      stickyActionBar: Container(
        color: Colors.white,
        child: Padding(
          padding: EdgeInsets.only(
              bottom: MySize.getScaledSizeHeight(10),
              left: MySize.getScaledSizeWidth(40),
              right: MySize.getScaledSizeWidth(40)),
          child: AppElevatedButton(
            TypoGraphy.text("Apply Filter",
                level: 3,
                fontWeight: FontWeight.w600,
                color: ColorConstant.white),
            onPressed: () {
              String startDate = '', endDate = '';
              List<int> selectedBatchIds = [];
              List<String> selectedSports = [];
              List<String> selectedPaymentStatus = [];
              for (int i = 0; i < isBatchSelected.length; i++) {
                if (batchData.value.isNotEmpty) {
                  if (isBatchSelected[i].value) {
                    selectedBatchIds.add(batchData.value[i].id);
                  }
                }
              }
              for (int i = 0; i < isPaymentStatusSelected.length; i++) {
                if (isPaymentStatusSelected[i].value) {
                  if (paymentStatusName[i] == "Paid") {
                    selectedPaymentStatus.add("Success");
                  }
                  if (paymentStatusName[i] == "Unpaid") {
                    selectedPaymentStatus.add("InProgress");
                  }
                  if (paymentStatusName[i] == "New Admission") {
                    selectedPaymentStatus.add("NewAdmission");
                  }
                  if (paymentStatusName[i] == "Renewals") {
                    selectedPaymentStatus.add("Renewal");
                  }
                  if (paymentStatusName[i] == "Installments") {
                    selectedPaymentStatus.add("Installment");
                  }
                  if (paymentStatusName[i] == "Extra Charge") {
                    selectedPaymentStatus.add("ExtraCharge");
                  }
                  if (paymentStatusName[i] == "Normal") {
                    selectedPaymentStatus.add("Normal");
                  }
                }
              }
              if (isAllTimeSelected.value) {
                startDate = DateFormat('yyyy-MM-dd').format(DateTime(2000));
                endDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
              }
              if (isLastMonthSelected.value) {
                startDate = DateFormat('yyyy-MM-dd').format(
                    DateTime(DateTime.now().year, DateTime.now().month - 1, 1));
                endDate = DateFormat('yyyy-MM-dd').format(
                    DateTime(DateTime.now().year, DateTime.now().month, 1)
                        .subtract(const Duration(days: 1)));
              }
              for (int i = 0; i < isSportSelected.length; i++) {
                if (isSportSelected[i].value) {
                  if (sports.value[i] == "Cricket") {
                    selectedSports.add("Cricket");
                  } else if (sports.value[i] == "Football") {
                    selectedSports.add("Football");
                  } else if (sports.value[i] == "Tennis") {
                    selectedSports.add("Tennis");
                  } else if (sports.value[i] == "Badminton") {
                    selectedSports.add("Badminton");
                  } else if (sports.value[i] == "Martial Arts") {
                    selectedSports.add("Martial Arts");
                  } else if (sports.value[i] == "Archery") {
                    selectedSports.add("Archery");
                  } else if (sports.value[i] == "Pilates") {
                    selectedSports.add("Pilates");
                  } else if (sports.value[i] == "Shooting") {
                    selectedSports.add("Shooting");
                  } else if (sports.value[i] == "Table Tennis") {
                    selectedSports.add("Table Tennis");
                  } else if (sports.value[i] == "Basketball") {
                    selectedSports.add("Basketball");
                  } else if (sports.value[i] == "Gymnastics") {
                    selectedSports.add("Gymnastics");
                  } else if (sports.value[i] == "Performance Ratings") {
                    selectedSports.add("SkillRating");
                  } else if (sports.value[i] == "Fees") {
                    selectedSports.add("Fee");
                  } else if (sports.value[i] == "Expenses") {
                    selectedSports.add("Expense");
                  } else if (sports.value[i] == "Attendance") {
                    selectedSports.add("Attendance");
                  } else if (sports.value[i] == "My Academy") {
                    selectedSports.add("Profile");
                  }
                }
              }
              ValueNotifier<bool> isFilterApplied = ValueNotifier(
                  selectedBatchIds.isNotEmpty ||
                      selectedBatchIds.isNotEmpty ||
                      selectedSports.isNotEmpty ||
                      selectedPaymentStatus.isNotEmpty ||
                      isAllTimeSelected.value ||
                      isLastMonthSelected.value ||
                      ids.isNotEmpty);
              onApply(ids, selectedBatchIds, selectedSports,
                  selectedPaymentStatus, startDate, endDate, isFilterApplied);
              context.pop();
            },
          ),
        ),
      ),
      child: BlocProvider(
        create: (context) => GetCenterBatchBloc(),
        child: Builder(builder: (context) {
          return BlocListener<GetCenterBatchBloc, GetCenterBatchState>(
            listener: (BuildContext context, GetCenterBatchState state) {
              if (state is GetBatchSuccess) {
                if (state.getBatchesModel.batchData.batches.length > 2) {
                  heightFactor.value = 0.7;
                }
                batchData.value = state.getBatchesModel.batchData.batches;
                log(batchData.value.toString());
                if (batchData.value.isNotEmpty) {
                  isAnyCenterSelected.value = true;
                }
                isBatchSelected.clear();
                isBatchSelected.addAll(List.generate(
                  state.getBatchesModel.batchData.batches.length,
                  (_) => ValueNotifier<bool>(false),
                ));
                if (ids.length > 1) {
                  for (var notifier in isBatchSelected) {
                    notifier.value = true;
                  }
                }
              }
            },
            child: Column(
              children: [
                TypoGraphy.text("Filter",
                    level: 3, fontWeight: FontWeight.w600),
                Gap(MySize.getScaledSizeHeight(12)),
                const Divider(
                    height: 2,
                    thickness: 2,
                    color: ColorConstant.textFieldColor),
                ExpansionTile(
                  controller: centerController,
                  iconColor: ColorConstant.signUp,
                  shape: const OutlineInputBorder(
                      borderSide: BorderSide(color: ColorConstant.transparent),
                      borderRadius: BorderRadius.all(Radius.circular(26))),
                  initiallyExpanded: true,
                  backgroundColor: ColorConstant.transparent,
                  title: TypoGraphy.text("Center",
                      level: 1,
                      fontWeight: FontWeight.w500,
                      textAlign: TextAlign.start),
                  children: List.generate(
                    centerData.length,
                    (index) => CheckBoxWidget(
                      onChange: () {
                        updateCenterSelection(index);
                        log(names.toString());
                        log(ids.toString());
                        context
                            .read<GetCenterBatchBloc>()
                            .add(GetBatches(id: ids));
                      },
                      title: centerData[index].name,
                      isSelected: isCenterSelected[index],
                    ),
                  ),
                ),
                const Divider(
                    height: 2,
                    thickness: 2,
                    color: ColorConstant.textFieldColor),
                Gap(MySize.getScaledSizeHeight(8)),
                const Divider(
                    height: 2,
                    thickness: 2,
                    color: ColorConstant.textFieldColor),
                if (isSportAvailable)
                  ValueListenableBuilder(
                    valueListenable: sports,
                    builder: (context, value, child) => ExpansionTile(
                      controller: sportController,
                      iconColor: ColorConstant.signUp,
                      shape: const OutlineInputBorder(
                          borderSide:
                              BorderSide(color: ColorConstant.transparent),
                          borderRadius: BorderRadius.all(Radius.circular(26))),
                      backgroundColor: ColorConstant.transparent,
                      title: TypoGraphy.text(isForCoach ? "Access" : "Sports",
                          level: 1,
                          fontWeight: FontWeight.w500,
                          textAlign: TextAlign.start),
                      children: List.generate(
                        isForCoach ? access.length : sports.value.length,
                        (index) => CheckBoxWidget(
                          title:
                              isForCoach ? access[index] : sports.value[index],
                          isSelected: isSportSelected[index],
                          onChange: () {
                            isSportSelected[index].value =
                                !isSportSelected[index].value;
                          },
                        ),
                      ),
                    ),
                  ),
                if (isSportAvailable)
                  const Divider(
                      height: 2,
                      thickness: 2,
                      color: ColorConstant.textFieldColor),
                if (isBatchAvailable) Gap(MySize.getScaledSizeHeight(8)),
                if (isBatchAvailable)
                  ValueListenableBuilder(
                    valueListenable: batchData,
                    builder: (context, batchData, child) =>
                        ValueListenableBuilder(
                            valueListenable: isAnyCenterSelected,
                            builder: (context, value, child) => value
                                ? const Divider(
                                    height: 2,
                                    thickness: 2,
                                    color: ColorConstant.textFieldColor)
                                : const SizedBox.shrink()),
                  ),
                if (isBatchAvailable)
                  ValueListenableBuilder(
                    valueListenable: isAnyCenterSelected,
                    builder: (context, value, child) => value
                        ? ExpansionTile(
                            controller: batchController,
                            iconColor: ColorConstant.signUp,
                            shape: const OutlineInputBorder(
                                borderSide: BorderSide(
                                    color: ColorConstant.transparent),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(26))),
                            backgroundColor: ColorConstant.transparent,
                            title: TypoGraphy.text("Batches",
                                level: 1,
                                fontWeight: FontWeight.w500,
                                textAlign: TextAlign.start),
                            children: [
                              ValueListenableBuilder<List<Batches>>(
                                valueListenable: batchData,
                                builder: (context, batches, child) {
                                  return Column(
                                    children: List.generate(
                                      batches.length,
                                      (index) => CheckBoxWidget(
                                        title: batches[index].name,
                                        isSelected: isBatchSelected[index],
                                        onChange: () {
                                          isBatchSelected[index].value =
                                              !isBatchSelected[index].value;
                                        },
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          )
                        : const SizedBox.shrink(),
                  ),
                if (isPaymentStatusAvailable)
                  const Divider(
                      height: 2,
                      thickness: 2,
                      color: ColorConstant.textFieldColor),
                if (isPaymentStatusAvailable)
                  ExpansionTile(
                    iconColor: ColorConstant.signUp,
                    shape: const OutlineInputBorder(
                        borderSide:
                            BorderSide(color: ColorConstant.transparent),
                        borderRadius: BorderRadius.all(Radius.circular(26))),
                    backgroundColor: ColorConstant.transparent,
                    title: TypoGraphy.text("Payment Type",
                        level: 1,
                        fontWeight: FontWeight.w500,
                        textAlign: TextAlign.start),
                    children: List.generate(
                      paymentStatusName.length,
                      (index) => CheckBoxWidget(
                        onChange: () {
                          isPaymentStatusSelected[index].value =
                              !isPaymentStatusSelected[index].value;
                        },
                        title: paymentStatusName[index],
                        isSelected: isPaymentStatusSelected[index],
                      ),
                    ),
                  ),
                if (isDurationAvailable)
                  const Divider(
                      height: 2,
                      thickness: 2,
                      color: ColorConstant.textFieldColor),
                if (isDurationAvailable)
                  ExpansionTile(
                      iconColor: ColorConstant.signUp,
                      shape: const OutlineInputBorder(
                          borderSide:
                              BorderSide(color: ColorConstant.transparent),
                          borderRadius: BorderRadius.all(Radius.circular(26))),
                      backgroundColor: ColorConstant.transparent,
                      title: TypoGraphy.text("Duration",
                          level: 1,
                          fontWeight: FontWeight.w500,
                          textAlign: TextAlign.start),
                      children: [
                        CheckBoxWidget(
                          onChange: () {
                            isAllTimeSelected.value = !isAllTimeSelected.value;
                            isLastMonthSelected.value = false;
                          },
                          title: "All Time",
                          isSelected: isAllTimeSelected,
                        ),
                        CheckBoxWidget(
                          onChange: () {
                            isLastMonthSelected.value =
                                !isLastMonthSelected.value;
                            isAllTimeSelected.value = false;
                          },
                          title: "Last Month",
                          isSelected: isLastMonthSelected,
                        ),
                      ]),
                Gap(MySize.getScaledSizeHeight(80))
              ],
            ),
          );
        }),
      ),
    );
  }

  static Future<void> planFilter(
    BuildContext context, {
    required void Function(
            ValueNotifier<bool> isNormalTypeSelected,
            ValueNotifier<bool> isInstallmentTypeSelected,
            ValueNotifier<bool> isSessionTypeSelected,
            ValueNotifier<bool> isMonthlyDurationSelected,
            ValueNotifier<bool> isQuarterlyDurationSelected,
            ValueNotifier<bool> is6MonthsDurationSelected,
            ValueNotifier<bool> isYearlyDurationSelected,
            ValueNotifier<bool> isCustomDurationSelected,
            ValueNotifier<bool> isFilterApplied,
            List<int> duration)
        onApply,
    required ValueNotifier<bool> isNormalTypeSelected,
    required ValueNotifier<bool> isInstallmentTypeSelected,
    required ValueNotifier<bool> isSessionTypeSelected,
    required ValueNotifier<bool> isMonthlyDurationSelected,
    required ValueNotifier<bool> isQuarterlyDurationSelected,
    required ValueNotifier<bool> is6MonthsDurationSelected,
    required ValueNotifier<bool> isYearlyDurationSelected,
    required ValueNotifier<bool> isCustomDurationSelected,

  }) async {
    showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: ColorConstant.white,
      context: context,
      builder: (context) {
        return CustomBottomSheetContainer(
          isForFilter: true,
          child: Column(
            children: [
              TypoGraphy.text("Filter", level: 3, fontWeight: FontWeight.w600),
              Gap(MySize.getScaledSizeHeight(12)),
              const Divider(
                  height: 2, thickness: 2, color: ColorConstant.textFieldColor),
              ExpansionTile(
                shape: const OutlineInputBorder(
                    borderSide: BorderSide(color: ColorConstant.transparent),
                    borderRadius: BorderRadius.all(Radius.circular(26))),
                initiallyExpanded: true,
                iconColor: ColorConstant.signUp,
                backgroundColor: ColorConstant.transparent,
                title: TypoGraphy.text("Plan Type",
                    level: 1,
                    fontWeight: FontWeight.w400,
                    textAlign: TextAlign.start),
                children: [
                  CheckBoxWidget(
                    title: "Normally",
                    isSelected: isNormalTypeSelected,
                    onChange: () {
                      isNormalTypeSelected.value = !isNormalTypeSelected.value;
                    },
                  ),
                  CheckBoxWidget(
                    title: "Installments",
                    isSelected: isInstallmentTypeSelected,
                    onChange: () {
                      isInstallmentTypeSelected.value =
                          !isInstallmentTypeSelected.value;
                    },
                  ),
                  CheckBoxWidget(
                    title: "Sessions",
                    isSelected: isSessionTypeSelected,
                    onChange: () {
                      isSessionTypeSelected.value =
                          !isSessionTypeSelected.value;
                    },
                  ),
                ],
              ),
              const Divider(
                  height: 2, thickness: 2, color: ColorConstant.textFieldColor),
              Gap(MySize.getScaledSizeHeight(8)),
              const Divider(
                  height: 2, thickness: 2, color: ColorConstant.textFieldColor),
              ExpansionTile(
                iconColor: ColorConstant.signUp,
                shape: const OutlineInputBorder(
                    borderSide: BorderSide(color: ColorConstant.transparent),
                    borderRadius: BorderRadius.all(Radius.circular(26))),
                initiallyExpanded: false,
                backgroundColor: ColorConstant.transparent,
                title: TypoGraphy.text("Duration",
                    level: 1,
                    fontWeight: FontWeight.w400,
                    textAlign: TextAlign.start),
                children: [
                  CheckBoxWidget(
                    title: "Monthly",
                    isSelected: isMonthlyDurationSelected,
                    onChange: () {
                      isMonthlyDurationSelected.value =
                          !isMonthlyDurationSelected.value;
                    },
                  ),
                  CheckBoxWidget(
                    title: "Quarterly",
                    isSelected: isQuarterlyDurationSelected,
                    onChange: () {
                      isQuarterlyDurationSelected.value =
                          !isQuarterlyDurationSelected.value;
                    },
                  ),
                  CheckBoxWidget(
                    title: "6-Months",
                    isSelected: is6MonthsDurationSelected,
                    onChange: () {
                      is6MonthsDurationSelected.value =
                          !is6MonthsDurationSelected.value;
                    },
                  ),
                  CheckBoxWidget(
                    title: "Yearly",
                    isSelected: isYearlyDurationSelected,
                    onChange: () {
                      isYearlyDurationSelected.value =
                          !isYearlyDurationSelected.value;
                    },
                  ),
                  CheckBoxWidget(
                    title: "Custom",
                    isSelected: isCustomDurationSelected,
                    onChange: () {
                      isCustomDurationSelected.value =
                          !isCustomDurationSelected.value;
                    },
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        isNormalTypeSelected.value = false;
                        isInstallmentTypeSelected.value = false;
                        isSessionTypeSelected.value = false;
                        isMonthlyDurationSelected.value = false;
                        isQuarterlyDurationSelected.value = false;
                        is6MonthsDurationSelected.value = false;
                        isYearlyDurationSelected.value = false;
                        isCustomDurationSelected.value = false;

                        List<int> durationType = [];
                        if (isMonthlyDurationSelected.value) {
                          durationType.add(1);
                        }
                        if (isQuarterlyDurationSelected.value) {
                          durationType.add(3);
                        }
                        if (is6MonthsDurationSelected.value) {
                          durationType.add(6);
                        }
                        if (isYearlyDurationSelected.value) {
                          durationType.add(12);
                        }
                        ValueNotifier<bool> isFilterApplied = ValueNotifier(
                            isInstallmentTypeSelected.value ||
                                isSessionTypeSelected.value ||
                                isMonthlyDurationSelected.value ||
                                isQuarterlyDurationSelected.value ||
                                is6MonthsDurationSelected.value ||
                                isYearlyDurationSelected.value ||
                                isCustomDurationSelected.value ||
                                isNormalTypeSelected.value);
                        onApply(
                            isNormalTypeSelected,
                            isInstallmentTypeSelected,
                            isSessionTypeSelected,
                            isMonthlyDurationSelected,
                            isQuarterlyDurationSelected,
                            is6MonthsDurationSelected,
                            isYearlyDurationSelected,
                            isCustomDurationSelected,
                            isFilterApplied,
                            durationType);
                        context.pop();
                      },
                      child: TypoGraphy.text("Reset",
                          level: 3, fontWeight: FontWeight.w500),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: AppElevatedButton(
                      TypoGraphy.text("Apply Filter",
                          level: 3,
                          fontWeight: FontWeight.w600,
                          color: ColorConstant.white),
                      onPressed: () {
                        ValueNotifier<bool> isFilterApplied = ValueNotifier(
                            isInstallmentTypeSelected.value ||
                                isSessionTypeSelected.value ||
                                isMonthlyDurationSelected.value ||
                                isQuarterlyDurationSelected.value ||
                                is6MonthsDurationSelected.value ||
                                isYearlyDurationSelected.value ||
                                isCustomDurationSelected.value ||
                                isNormalTypeSelected.value);
                        List<int> durationType = [];
                        if (isMonthlyDurationSelected.value) {
                          durationType.add(1);
                        }
                        if (isQuarterlyDurationSelected.value) {
                          durationType.add(3);
                        }
                        if (is6MonthsDurationSelected.value) {
                          durationType.add(6);
                        }
                        if (isYearlyDurationSelected.value) {
                          durationType.add(12);
                        }
                        onApply(
                            isNormalTypeSelected,
                            isInstallmentTypeSelected,
                            isSessionTypeSelected,
                            isMonthlyDurationSelected,
                            isQuarterlyDurationSelected,
                            is6MonthsDurationSelected,
                            isYearlyDurationSelected,
                            isCustomDurationSelected,
                            isFilterApplied,
                            durationType);
                        context.pop();
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  static showReportDuration(
    BuildContext context, {
    required String initialSelectedDuration,
    required DateTime startDate,
    bool isForFees = false,
    required DateTime endDate,
    required void Function(String duration, String dateRange,
            DateTime startDate, DateTime endDate)
        onTap,
  }) {
    showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: ColorConstant.white,
      context: context,
      builder: (context) {
        ValueNotifier<String> selectedDuration =
            ValueNotifier(initialSelectedDuration);
        DateTime selectedStartDate = startDate;
        DateTime selectedEndDate = endDate;

        return ValueListenableBuilder(
          valueListenable: selectedDuration,
          builder: (context, value, child) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18.0),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Gap(MySize.getScaledSizeHeight(20)),
                  TypoGraphy.text("Duration",
                      level: 3, fontWeight: FontWeight.w600),
                  Gap(MySize.getScaledSizeHeight(35)),
                  RadioHelper(
                    selectedDuration: selectedDuration,
                    setValue: "Today",
                  ),
                  if (isForFees) Gap(MySize.getScaledSizeHeight(10)),
                  if (isForFees)
                    RadioHelper(
                      selectedDuration: selectedDuration,
                      setValue: "Last 7 Days",
                    ),
                  Gap(MySize.getScaledSizeHeight(10)),
                  RadioHelper(
                    selectedDuration: selectedDuration,
                    setValue: 'Current Month',
                  ),
                  Gap(MySize.getScaledSizeHeight(10)),
                  RadioHelper(
                    selectedDuration: selectedDuration,
                    setValue: 'Last Month',
                  ),
                  Gap(MySize.getScaledSizeHeight(10)),
                  RadioHelper(
                    selectedDuration: selectedDuration,
                    setValue: 'Custom',
                  ),
                  Visibility(
                    visible: selectedDuration.value == 'Custom',
                    child: Column(
                      children: [
                        Gap(MySize.getScaledSizeHeight(10)),
                        Container(
                          decoration: BoxDecoration(
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(10)),
                              border:
                                  Border.all(color: ColorConstant.greyColor)),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: SfDateRangePicker(
                              selectionColor: ColorConstant.gradient1,
                              todayHighlightColor: ColorConstant.gradient1,
                              rangeSelectionColor: ColorConstant.skyBlue,
                              initialSelectedRange: PickerDateRange(
                                startDate,
                                endDate,
                              ),
                              initialSelectedDate: startDate,
                              initialDisplayDate: endDate,
                              startRangeSelectionColor: ColorConstant.signUp,
                              endRangeSelectionColor: ColorConstant.signUp,
                              onSelectionChanged:
                                  (DateRangePickerSelectionChangedArgs args) {
                                final PickerDateRange dateRange = args.value;
                                if (args.value.startDate != null &&
                                    args.value.endDate != null) {
                                  selectedStartDate = dateRange.startDate!;
                                  selectedEndDate = dateRange.endDate!;
                                }
                              },
                              selectionMode: DateRangePickerSelectionMode.range,
                              headerStyle: const DateRangePickerHeaderStyle(
                                  textAlign: TextAlign.center,
                                  backgroundColor: ColorConstant.transparent),
                              backgroundColor: ColorConstant.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(MySize.getScaledSizeHeight(10)),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 18.0),
                    child: AppElevatedButton(
                      TypoGraphy.text("Select Duration",
                          color: ColorConstant.white, level: 3),
                      onPressed: () {
                        DateTime now = DateTime.now();
                        onTap(
                          selectedDuration.value,
                          selectedDuration.value == "Custom"
                              ? "${DateFormat("dd/MM/yyyy").format(selectedStartDate)} - ${DateFormat("dd/MM/yyyy").format(selectedEndDate)}"
                              : selectedDuration.value,
                          selectedDuration.value == "Last 7 Days"
                              ? now.subtract(const Duration(days: 7))
                              : selectedDuration.value == "Current Month"
                                  ? DateTime(now.year, now.month, 1)
                                  : selectedDuration.value == "Last Month"
                                      ? DateTime(now.year, now.month - 1, 1)
                                      : selectedDuration.value == "Today"
                                          ? DateTime(
                                              now.year, now.month, now.day)
                                          : selectedStartDate,
                          selectedDuration.value == "Last 7 Days"
                              ? now
                              : selectedDuration.value == "Current Month"
                                  ? now
                                  : selectedDuration.value == "Last Month"
                                      ? DateTime(now.year, now.month, 0)
                                      : selectedDuration.value == "Today"
                                          ? DateTime(
                                              now.year, now.month, now.day)
                                          : selectedEndDate,
                        );
                        context.pop();
                      },
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static showReportType(
    BuildContext context, {
    required String initialSelectedReportType,
    required void Function(String duration) onTap,
    bool isForStudent = false,
  }) async {
    showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: ColorConstant.white,
      context: context,
      builder: (context) {
        ValueNotifier<String> selectedDuration =
            ValueNotifier(initialSelectedReportType);
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 18.0),
          child: SingleChildScrollView(
            child: ValueListenableBuilder(
              valueListenable: selectedDuration,
              builder: (context, value, child) => Column(
                children: [
                  Gap(MySize.getScaledSizeHeight(20)),
                  TypoGraphy.text("Select Report Type",
                      level: 3, fontWeight: FontWeight.w600),
                  Gap(MySize.getScaledSizeHeight(35)),
                  RadioHelper(
                    selectedDuration: selectedDuration,
                    setValue: isForStudent ? "Combined" : 'Collection Report',
                  ),
                  Gap(MySize.getScaledSizeHeight(10)),
                  RadioHelper(
                    selectedDuration: selectedDuration,
                    setValue:
                        isForStudent ? "Individual" : 'Transaction Report',
                  ),
                  Gap(MySize.getScaledSizeHeight(10)),
                  // RadioHelper(
                  //   selectedDuration: selectedDuration,
                  //   setValue: 'Extra Charges Report',
                  // ),
                  Gap(MySize.getScaledSizeHeight(15)),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 18.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              selectedDuration.value == "";
                              context.pop();
                            },
                            child: TypoGraphy.text("Cancel",
                                level: 3, fontWeight: FontWeight.w500),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: AppElevatedButton(
                            TypoGraphy.text("Select",
                                level: 3,
                                fontWeight: FontWeight.w600,
                                color: ColorConstant.white),
                            onPressed: () {
                              onTap(selectedDuration.value);
                              context.pop();
                            },
                          ),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  static showReportSubType(
    BuildContext context, {
    required String initialSelectedReportSubType,
    bool isForTransaction = false,
    required void Function(String subType) onTap,
  }) async {
    showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: ColorConstant.white,
      context: context,
      builder: (context) {
        ValueNotifier<String> selectedSubType =
            ValueNotifier(initialSelectedReportSubType);
        return ValueListenableBuilder(
          valueListenable: selectedSubType,
          builder: (context, value, child) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 18.0),
            child: SingleChildScrollView(
              child: isForTransaction
                  ? Column(
                      children: [
                        Gap(MySize.getScaledSizeHeight(20)),
                        TypoGraphy.text("Select Sub Type",
                            level: 3, fontWeight: FontWeight.w600),
                        Gap(MySize.getScaledSizeHeight(35)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'Combined Report',
                        ),
                        Gap(MySize.getScaledSizeHeight(10)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'UPI Collection',
                        ),
                        Gap(MySize.getScaledSizeHeight(10)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'Cash Collection',
                        ),
                        Gap(MySize.getScaledSizeHeight(10)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'NEFT Collection',
                        ),
                        Gap(MySize.getScaledSizeHeight(10)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'IMPS Collection',
                        ),
                        Gap(MySize.getScaledSizeHeight(10)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'Cheque Collection',
                        ),
                        Gap(MySize.getScaledSizeHeight(20)),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 18.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    selectedSubType.value = '';
                                    context.pop();
                                  },
                                  child: TypoGraphy.text("Cancel",
                                      level: 3, fontWeight: FontWeight.w500),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: AppElevatedButton(
                                  TypoGraphy.text("Select",
                                      level: 3,
                                      fontWeight: FontWeight.w600,
                                      color: ColorConstant.white),
                                  onPressed: () {
                                    onTap(selectedSubType.value);
                                    context.pop();
                                  },
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    )
                  : Column(
                      children: [
                        Gap(MySize.getScaledSizeHeight(20)),
                        TypoGraphy.text("Select Sub Type",
                            level: 3, fontWeight: FontWeight.w600),
                        Gap(MySize.getScaledSizeHeight(35)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'Combined Report',
                        ),
                        Gap(MySize.getScaledSizeHeight(10)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'Installment Report',
                        ),
                        Gap(MySize.getScaledSizeHeight(10)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'New Admission Report',
                        ),
                        Gap(MySize.getScaledSizeHeight(10)),
                        RadioHelper(
                          selectedDuration: selectedSubType,
                          setValue: 'Renewals Report',
                        ),
                        Gap(MySize.getScaledSizeHeight(20)),
                        Padding(
                          padding: const EdgeInsets.only(bottom: 18.0),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {},
                                  child: TypoGraphy.text("Cancel",
                                      level: 3, fontWeight: FontWeight.w500),
                                ),
                              ),
                              Expanded(
                                flex: 2,
                                child: AppElevatedButton(
                                  TypoGraphy.text("Select",
                                      level: 3,
                                      fontWeight: FontWeight.w600,
                                      color: ColorConstant.white),
                                  onPressed: () {
                                    onTap(selectedSubType.value);
                                    context.pop();
                                  },
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
            ),
          ),
        );
      },
    );
  }

  static extraCharge(
    BuildContext context, {
    required List<ChargeDetails> assignCharge,
    required int stdId,
    required void Function() onTap,
    required ExpansionTileController controller,
    required final List<ValueNotifier<bool>> isChargeSelected,
  }) {
    String transactionDate = DateFormat('dd/MM/yyyy').format(DateTime.now());
    ValueNotifier<bool> isCashSelected = ValueNotifier(true),
        isUpiSelected = ValueNotifier(false),
        isTransactionVisible = ValueNotifier(false),
        isChequeSelected = ValueNotifier(false),
        isNEFTSelected = ValueNotifier(false),
        isIMPSSelected = ValueNotifier(false);
    ValueNotifier<List<ChargeDetails>> selectedCharge = ValueNotifier([]);
    List<ValueNotifier<int>> quantities =
        List.generate(assignCharge.length, (index) => ValueNotifier<int>(1));

    TextEditingController transactionIdController = TextEditingController();
    // final ExpansionTileController controller = ExpansionTileController();
    int calculateTotalTax(List<ChargeDetails> charges) {
      int totalTaxAmount = 0;
      for (var charge in charges) {
        if (charge.tax > 0) {
          int taxAmount = (charge.amount * charge.tax) ~/ 100;
          totalTaxAmount += taxAmount;
        }
      }
      return totalTaxAmount;
    }

    showModalBottomSheet(
      backgroundColor: ColorConstant.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(13),
        ),
      ),
      context: context,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: BlocProvider(
            create: (context) => RenewStudentBloc(),
            child: BlocListener<RenewStudentBloc, RenewStudentState>(
              listener: (BuildContext context, RenewStudentState state) {
                if (state is MakePaymentSuccess) {
                  CustomDialog.hideLoader(context);
                  selectedCharge.value.clear();
                  onTap();
                  context.pop();
                } else if (state is RenewStudentFailure) {
                  CustomDialog.hideLoader(context);
                  ToastUtils.showFailed(message: state.msg);
                } else if (state is RenewStudentLoading) {
                  CustomDialog.showLoader(context);
                }
              },
              child: Builder(builder: (context) {
                return StatefulBuilder(
                  builder: (context, setState) {
                    return Padding(
                      padding: EdgeInsets.all(MySize.size8 ?? 8),
                      child: Column(
                        children: [
                          Expanded(
                              child: ListView(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const SizedBox.shrink(),
                                  Padding(
                                    padding: EdgeInsets.only(
                                        left: MySize.getScaledSizeWidth(50)),
                                    child: Column(
                                      children: [
                                        Container(
                                          width: MySize.getScaledSizeWidth(27),
                                          height: MySize.getScaledSizeHeight(3),
                                          decoration: const BoxDecoration(
                                              color:
                                                  ColorConstant.textFieldColor,
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(23))),
                                        ),
                                        Gap(MySize.getScaledSizeHeight(3)),
                                        Container(
                                          width: MySize.getScaledSizeWidth(19),
                                          height: MySize.getScaledSizeHeight(3),
                                          decoration: const BoxDecoration(
                                              color:
                                                  ColorConstant.textFieldColor,
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(23))),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(
                                        right: MySize.size8 ?? 8,
                                        top: MySize.size8 ?? 8),
                                    child: GestureDetector(
                                      onTap: () {
                                        Navigator.pop(context);
                                      },
                                      child: CircleAvatar(
                                        radius: 12,
                                        backgroundColor:
                                            ColorConstant.textFieldColor,
                                        child: CustomSvgPicture(
                                          AppAsset.crossIcon,
                                          height:
                                              MySize.getScaledSizeHeight(12),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Gap(MySize.getScaledSizeHeight(20)),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  TypoGraphy.text("Transaction Date : ",
                                      color: ColorConstant.gray,
                                      fontSize: MySize.getScaledSizeHeight(12),
                                      fontWeight: FontWeight.w500),
                                  const Expanded(child: SizedBox.shrink()),
                                  TypoGraphy.text(transactionDate,
                                      level: 1, fontWeight: FontWeight.w500),
                                  Gap(MySize.getScaledSizeWidth(5)),
                                  GestureDetector(
                                    onTap: () async {
                                      DateTime initialDate = DateTime.now();
                                      DateTime? pickedDate =
                                          await showDatePicker(
                                        builder: (context, child) {
                                          return Theme(
                                              data: Theme.of(context).copyWith(
                                                  colorScheme:
                                                      const ColorScheme.light(
                                                    primary:
                                                        ColorConstant.signUp,
                                                    onPrimary: Colors.white,
                                                    onSurface: Colors
                                                        .black, // Month days , years
                                                  ),
                                                  textButtonTheme:
                                                      TextButtonThemeData(
                                                    style: TextButton.styleFrom(
                                                      foregroundColor: ColorConstant
                                                          .signUp, // ok , cancel    buttons
                                                    ),
                                                  ),
                                                  dialogBackgroundColor:
                                                      ColorConstant.white),
                                              child: child!);
                                        },
                                        context: context,
                                        initialDate: transactionDate != ""
                                            ? initialDate
                                            : DateFormat('dd/MM/yyyy')
                                                .parse(transactionDate),
                                        firstDate: DateTime(1800),
                                        lastDate: DateTime(2030),
                                      );
                                      if (pickedDate != null) {
                                        setState(() {
                                          transactionDate =
                                              DateFormat('dd/MM/yyyy')
                                                  .format(pickedDate);
                                          initialDate = pickedDate;
                                        });
                                      }
                                    },
                                    child: CustomSvgPicture(
                                      AppAsset.editIconSvg,
                                      height: MySize.getScaledSizeHeight(16),
                                    ),
                                  ),
                                ],
                              ),
                              Gap(MySize.getScaledSizeHeight(20)),
                              SizedBox(
                                height: MySize.getScaledSizeHeight(39),
                                child: ListView(
                                  scrollDirection: Axis.horizontal,
                                  children: [
                                    PaymentTypeContainer(
                                      image: AppAsset.cashSvg,
                                      isSelected: isCashSelected,
                                      title: 'Cash',
                                      onTap: () {
                                        isCashSelected.value = true;
                                        isUpiSelected.value = false;
                                        isChequeSelected.value = false;
                                        isNEFTSelected.value = false;
                                        isIMPSSelected.value = false;
                                      },
                                    ),
                                    Gap(MySize.getScaledSizeWidth(10)),
                                    PaymentTypeContainer(
                                      image: AppAsset.upiId,
                                      isSelected: isUpiSelected,
                                      title: 'UPI',
                                      onTap: () {
                                        isUpiSelected.value = true;
                                        isCashSelected.value = false;
                                        isChequeSelected.value = false;
                                        isNEFTSelected.value = false;
                                        isIMPSSelected.value = false;
                                      },
                                    ),
                                    Gap(MySize.getScaledSizeWidth(10)),
                                    PaymentTypeContainer(
                                      image: AppAsset.otherPayment,
                                      isSelected: isIMPSSelected,
                                      title: 'IMPS',
                                      onTap: () {
                                        isChequeSelected.value = false;
                                        isNEFTSelected.value = false;
                                        isIMPSSelected.value = true;
                                        isCashSelected.value = false;
                                        isUpiSelected.value = false;
                                      },
                                    ),
                                    Gap(MySize.getScaledSizeWidth(10)),
                                    PaymentTypeContainer(
                                      image: AppAsset.neft,
                                      isSelected: isNEFTSelected,
                                      title: 'NEFT',
                                      onTap: () {
                                        isChequeSelected.value = false;
                                        isNEFTSelected.value = true;
                                        isIMPSSelected.value = false;
                                        isCashSelected.value = false;
                                        isUpiSelected.value = false;
                                      },
                                    ),
                                    Gap(MySize.getScaledSizeWidth(10)),
                                    PaymentTypeContainer(
                                      image: AppAsset.cheque,
                                      isSelected: isChequeSelected,
                                      title: 'Cheque',
                                      onTap: () {
                                        isChequeSelected.value = true;
                                        isNEFTSelected.value = false;
                                        isIMPSSelected.value = false;
                                        isCashSelected.value = false;
                                        isUpiSelected.value = false;
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              Gap(MySize.getScaledSizeHeight(20)),
                              Card(
                                borderOnForeground: true,
                                color: ColorConstant.dashBoardTextField,
                                elevation: 0,
                                shape: RoundedRectangleBorder(
                                  side: const BorderSide(
                                      color: ColorConstant.textFieldColor),
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                clipBehavior: Clip.antiAlias,
                                margin: EdgeInsets.zero,
                                child: ValueListenableBuilder(
                                  valueListenable: selectedCharge,
                                  builder: (context, value, child) =>
                                      ExpansionTile(
                                    initiallyExpanded: true,
                                    controller: controller,
                                    title: TypoGraphy.text(
                                      selectedCharge.value.isEmpty
                                          ? "Add Extra charges"
                                          : "Charge Added",
                                      fontWeight: FontWeight.w500,
                                      level: 1,
                                      textAlign: TextAlign.start,
                                    ),
                                    leading: const CustomSvgPicture(
                                        AppAsset.tileLeadingIcon),
                                    trailing:
                                        const CustomSvgPicture(AppAsset.plus),
                                    children: [
                                      ...List.generate(
                                        assignCharge.length,
                                        (index) => ExtraChargeHelper(
                                          bottomLeft: index ==
                                                  assignCharge.indexOf(
                                                      assignCharge.last)
                                              ? 15
                                              : 0,
                                          bottomRight: index ==
                                                  assignCharge.indexOf(
                                                      assignCharge.last)
                                              ? 15
                                              : 0,
                                          topRight: index ==
                                                  assignCharge.indexOf(
                                                      assignCharge.first)
                                              ? 15
                                              : 0,
                                          topLeft: index ==
                                                  assignCharge.indexOf(
                                                      assignCharge.first)
                                              ? 15
                                              : 0,
                                          isSelected:
                                              isChargeSelected.elementAt(index),
                                          chargeName: assignCharge[index].name,
                                          amount: assignCharge[index]
                                              .amount
                                              .toString(),
                                          quantity: quantities[index],
                                        ),
                                      ),
                                      Gap(MySize.getScaledSizeHeight(10)),
                                      GestureDetector(
                                        onTap: () {
                                          selectedCharge.value.clear();
                                          for (int i = 0;
                                              i < isChargeSelected.length;
                                              i++) {
                                            if (isChargeSelected
                                                .elementAt(i)
                                                .value) {
                                              selectedCharge.value.add(ChargeDetails(
                                                  quantity: quantities[i].value,
                                                  id: assignCharge[i].id,
                                                  hsnCode:
                                                      assignCharge[i].hsnCode,
                                                  name: assignCharge[i].name,
                                                  amount: quantities[i].value >
                                                          1
                                                      ? (assignCharge[i]
                                                              .amount *
                                                          quantities[i].value)
                                                      : assignCharge[i].amount,
                                                  createdAt:
                                                      assignCharge[i].createdAt,
                                                  updatedAt:
                                                      assignCharge[i].updatedAt,
                                                  deletedAt:
                                                      assignCharge[i].deletedAt,
                                                  academyId:
                                                      assignCharge[i].academyId,
                                                  tax: assignCharge[i].tax));
                                              log(selectedCharge.toString());
                                              controller.collapse();
                                            }
                                          }
                                          if (selectedCharge.value.isNotEmpty) {
                                            setState(() {});
                                          } else {
                                            setState(() {
                                              selectedCharge.value.clear();
                                            });
                                            controller.collapse();
                                          }
                                        },
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                              bottom:
                                                  MySize.getScaledSizeHeight(
                                                      12.0),
                                              right:
                                                  MySize.getScaledSizeWidth(10),
                                              left: MySize.getScaledSizeWidth(
                                                  10)),
                                          child: Container(
                                              width: MySize.getScaledSizeWidth(
                                                  342),
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      50),
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      const BorderRadius.all(
                                                          Radius.circular(100)),
                                                  border: Border.all(
                                                      color: ColorConstant
                                                          .signUp)),
                                              child: Center(
                                                  child: TypoGraphy.text("Done",
                                                      color: ColorConstant
                                                          .signUp))),
                                        ),
                                      ),
                                      Gap(MySize.getScaledSizeHeight(20)),
                                    ],
                                  ),
                                ),
                              ),
                              Gap(MySize.getScaledSizeHeight(20)),
                              ValueListenableBuilder(
                                valueListenable: selectedCharge,
                                builder: (context, value, child) => selectedCharge
                                        .value.isNotEmpty
                                    ? Column(
                                        children: List.generate(
                                          selectedCharge.value.length,
                                          (index) => Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              TypoGraphy.text(
                                                  "${selectedCharge.value[index].name} ${quantities[index].value > 1 ? "X ${quantities[index].value}" : ""}:",
                                                  fontSize: MySize
                                                      .getScaledSizeHeight(12),
                                                  fontWeight: FontWeight.w500),
                                              TypoGraphy.text(
                                                  "₹ ${selectedCharge.value[index].amount}",
                                                  fontSize: MySize
                                                      .getScaledSizeHeight(12),
                                                  fontWeight: FontWeight.w500),
                                            ],
                                          ),
                                        ),
                                      )
                                    // ? Padding(
                                    //     padding: const EdgeInsets.all(8.0),
                                    //     child: Row(
                                    //       mainAxisAlignment:
                                    //           MainAxisAlignment
                                    //               .spaceBetween,
                                    //       children: [
                                    //         TypoGraphy.text(
                                    //             "$chargesApplied :",
                                    //             fontSize: MySize
                                    //                 .getScaledSizeHeight(
                                    //                     12),
                                    //             fontWeight:
                                    //                 FontWeight.w500),
                                    //         TypoGraphy.text(
                                    //             "₹ $chargeAmount",
                                    //             fontSize: MySize
                                    //                 .getScaledSizeHeight(
                                    //                     12),
                                    //             fontWeight:
                                    //                 FontWeight.w500),
                                    //       ],
                                    //     ),
                                    //   )
                                    : const SizedBox.shrink(),
                              ),
                              Gap(MySize.getScaledSizeHeight(20)),
                              ValueListenableBuilder(
                                valueListenable: selectedCharge,
                                builder: (context, value, child) => Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const CustomSvgPicture(AppAsset.rupeeSvg),
                                    TypoGraphy.text(
                                        selectedCharge.value.isNotEmpty
                                            ? ((selectedCharge.value
                                                        .map((e) => e.amount)
                                                        .reduce(
                                                          (value, element) =>
                                                              value + element,
                                                        )) +
                                                    calculateTotalTax(
                                                        selectedCharge.value))
                                                .toString()
                                            : "0",
                                        fontWeight: FontWeight.w500,
                                        color: ColorConstant.black,
                                        fontSize:
                                            MySize.getScaledSizeHeight(28)),
                                    TypoGraphy.text(
                                        "(${calculateTotalTax(selectedCharge.value)} GST)",
                                        fontWeight: FontWeight.w500,
                                        color: const Color(0XFF929292),
                                        fontSize:
                                            MySize.getScaledSizeHeight(15)),
                                  ],
                                ),
                              ),
                              Gap(MySize.getScaledSizeHeight(5)),
                              TypoGraphy.text("Total Amount",
                                  fontSize: MySize.getScaledSizeHeight(12),
                                  fontWeight: FontWeight.w500,
                                  color: ColorConstant.gray),
                              Gap(MySize.getScaledSizeHeight(15)),
                              ValueListenableBuilder(
                                valueListenable: isTransactionVisible,
                                builder: (context, value, child) => !value
                                    ? GestureDetector(
                                        onTap: () {
                                          isTransactionVisible.value = true;
                                        },
                                        child: GradientText(
                                          gradient:
                                              const LinearGradient(colors: [
                                            ColorConstant.white,
                                            ColorConstant.primaryColor,
                                          ]),
                                          child: TypoGraphy.text(
                                              "Add Transaction ID(Optional)",
                                              color: ColorConstant.primaryColor,
                                              fontSize:
                                                  MySize.getScaledSizeHeight(
                                                      12),
                                              fontWeight: FontWeight.w500),
                                        ),
                                      )
                                    : SizedBox(
                                        child: OutlinedTextFormField(
                                          horizontalPadding: 0,
                                          fillColor:
                                              ColorConstant.textFieldColor,
                                          controller: transactionIdController,
                                          maxLines: 2,
                                          isOnboard: true,
                                          hintText:
                                              "Add Transaction ID(Optional)",
                                        ),
                                      ),
                              ),
                              Gap(MySize.getScaledSizeHeight(20)),
                            ],
                          )),
                          Padding(
                            padding: EdgeInsets.only(
                                bottom: MySize.getScaledSizeHeight(18.0)),
                            child: AppElevatedButton(
                              width: MySize.getScaledSizeWidth(365),
                              TypoGraphy.text("Mark Payment received",
                                  color: ColorConstant.white, level: 3),
                              onPressed: () {
                                log(selectedCharge.value.toString());
                                context.read<RenewStudentBloc>().add(
                                    MakePayment(
                                        amount: ((selectedCharge.value
                                                .map((e) => e.amount)
                                                .reduce(
                                                  (value, element) =>
                                                      value + element,
                                                )) +
                                            calculateTotalTax(
                                                selectedCharge.value)),
                                        studentId: stdId,
                                        transactionId:
                                            transactionIdController.text,
                                        charge:
                                            selectedCharge.value.map((charge) {
                                          return ExtraCharge(
                                            quantity: charge.quantity,
                                            id: charge.id,
                                            hsnCod: charge.hsnCode,
                                            name: charge.name,
                                            amount: charge.amount ~/
                                                (charge.quantity == 0
                                                    ? 1
                                                    : charge.quantity),
                                            tax: charge.tax,
                                          );
                                        }).toList(),
                                        paymentMethod: isCashSelected.value
                                            ? "Cash"
                                            : isUpiSelected.value
                                                ? "UPI"
                                                : isChequeSelected.value
                                                    ? "Cheque"
                                                    : isNEFTSelected.value
                                                        ? "NEFT"
                                                        : isIMPSSelected.value
                                                            ? "IMPS"
                                                            : "",
                                        transactionDate:
                                            DateFormat('yyyy-MM-dd').format(
                                                DateFormat("dd/MM/yyyy")
                                                    .parse(transactionDate)),
                                        name: selectedCharge.value.length == 1
                                            ? selectedCharge.value[0].name
                                            : "${selectedCharge.value[0].name} + ${selectedCharge.value.length - 1} Other"));
                              },
                            ),
                          )
                        ],
                      ),
                    );
                  },
                );
              }),
            ),
          ),
        );
      },
    );
  }

  static Future<void> stdInstallment(BuildContext context,
      {required PaymentInstallment paymentInstallment,
      required List<InstallmentData> installmentData,
      required String renewDate,
      required ValueNotifier<bool> hasPlaChanges,
      required int advanceAmount}) async {
    DateTime renewDateTime = DateTime.parse(renewDate);

    showModalBottomSheet(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(10),
        ),
      ),
      backgroundColor: ColorConstant.white,
      context: context,
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0),
          child: Column(
            children: [
              Gap(MySize.getScaledSizeHeight(25)),
              TypoGraphy.text("Installment",
                  level: 1, fontWeight: FontWeight.w700),
              Gap(MySize.getScaledSizeHeight(30)),
              Expanded(
                child: ListView(
                  children: [
                    if (advanceAmount != 0)
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2.0),
                        child: InstallmentTile(
                          date: "Advance",
                          bottomLeft: 15,
                          bottomRight: 15,
                          topRight: 15,
                          topLeft: 15,
                          amount: advanceAmount,
                          status: !hasPlaChanges.value
                              ? installmentData.isNotEmpty &&
                                      installmentData.first.type == "Advance" &&
                                      installmentData.first.status == "Success"
                                  ? "Paid"
                                  : DateFormat("MM-yyyy")
                                              .format(renewDateTime) ==
                                          DateFormat("MM-yyyy")
                                              .format(DateTime.now())
                                      ? "On Going"
                                      : "Pending"
                              : "Pending",
                          transactionDate:
                              DateFormat("dd/MM/yyyy").format(renewDateTime),
                        ),
                      ),
                    ...List.generate(
                      paymentInstallment.installment.length,
                      (index) {
                        String installmentLabel;
                        if (index == 0) {
                          installmentLabel = "1st Installment";
                        } else if (index == 1) {
                          installmentLabel = "2nd Installment";
                        } else if (index == 2) {
                          installmentLabel = "3rd Installment";
                        } else {
                          installmentLabel = "${index + 1}th Installment";
                        }

                        DateTime transactionDate = advanceAmount != 0
                            ? renewDateTime
                                .add(Duration(days: 30 * (index + 1)))
                            : renewDateTime
                                .add(Duration(days: 30 * (index + 0)));

                        String status = "Pending";

                        int dataIndex = advanceAmount != 0 ? index + 1 : index;

                        if (!hasPlaChanges.value) {
                          if (dataIndex < installmentData.length) {
                            if (installmentData[dataIndex].status ==
                                "Success") {
                              status = "Paid";
                            } else if (DateFormat("MM-yyyy")
                                    .format(transactionDate) ==
                                DateFormat("MM-yyyy").format(DateTime.now())) {
                              status = "On Going";
                            }
                          } else {
                            if (DateFormat("MM-yyyy").format(transactionDate) ==
                                DateFormat("MM-yyyy").format(DateTime.now())) {
                              status = "On Going";
                            }
                          }
                        }
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2.0),
                          child: InstallmentTile(
                            date: installmentLabel,
                            bottomLeft: index ==
                                    paymentInstallment.installment.indexOf(
                                        paymentInstallment.installment.last)
                                ? 15
                                : 15,
                            bottomRight: index ==
                                    paymentInstallment.installment.indexOf(
                                        paymentInstallment.installment.last)
                                ? 15
                                : 15,
                            topRight: index ==
                                    paymentInstallment.installment.indexOf(
                                        paymentInstallment.installment.first)
                                ? 15
                                : 15,
                            topLeft: index ==
                                    paymentInstallment.installment.indexOf(
                                        paymentInstallment.installment.first)
                                ? 15
                                : 15,
                            amount: paymentInstallment.installment[index],
                            status: status,
                            transactionDate: DateFormat("dd/MM/yyyy")
                                .format(transactionDate),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class InstallmentTile extends StatelessWidget {
  final double topLeft, topRight, bottomRight, bottomLeft;
  final int amount;
  final String status;
  final String date, transactionDate;

  const InstallmentTile({
    super.key,
    this.topLeft = 15,
    this.topRight = 15,
    this.bottomRight = 15,
    this.bottomLeft = 15,
    required this.amount,
    required this.date,
    required this.status,
    required this.transactionDate,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      borderOnForeground: true,
      color: ColorConstant.dashBoardTextField,
      elevation: 0,
      shape: RoundedRectangleBorder(
        side: const BorderSide(color: ColorConstant.textFieldColor),
        borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(bottomLeft),
            bottomRight: Radius.circular(bottomRight),
            topLeft: Radius.circular(topLeft),
            topRight: Radius.circular(topRight)),
      ),
      clipBehavior: Clip.antiAlias,
      margin: EdgeInsets.zero,
      child: ExpansionTile(
        shape: OutlineInputBorder(
          borderSide: const BorderSide(color: ColorConstant.textFieldColor),
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(bottomLeft),
              bottomRight: Radius.circular(bottomRight),
              topLeft: Radius.circular(topLeft),
              topRight: Radius.circular(topRight)),
        ),
        collapsedBackgroundColor: ColorConstant.dashBoardTextField,
        backgroundColor: ColorConstant.dashBoardTextField,
        title: Row(
          children: [
            TypoGraphy.text(date, level: 1),
            const Expanded(child: SizedBox.shrink()),
            GradientText(
                gradient: const LinearGradient(colors: [
                  ColorConstant.white,
                  ColorConstant.primaryColor,
                ]),
                child: TypoGraphy.text("₹ $amount",
                    color: ColorConstant.primaryColor,
                    level: 1,
                    fontWeight: FontWeight.w500))
          ],
        ),
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size10 ?? 10)
                .copyWith(
                    bottom: MySize.getScaledSizeHeight(
                        MySize.getScaledSizeHeight(10))),
            child: Container(
              decoration: BoxDecoration(
                  color: ColorConstant.white,
                  border: Border.all(color: ColorConstant.textFieldColor),
                  borderRadius: const BorderRadius.all(Radius.circular(25))),
              child: Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: MySize.getScaledSizeWidth(25),
                    vertical: MySize.getScaledSizeHeight(15)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TypoGraphy.text(status,
                        fontWeight: FontWeight.w700,
                        color: status == "Paid"
                            ? const Color(0XFF1B944C)
                            : status == "On Going"
                                ? const Color(0XFFFB923C)
                                : const Color(0XFFDC2626),
                        fontSize: MySize.getScaledSizeHeight(14)),
                    TypoGraphy.text(transactionDate,
                        fontSize: MySize.getScaledSizeHeight(14),
                        fontWeight: FontWeight.w500)
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class RadioHelper extends StatelessWidget {
  final ValueNotifier<String> selectedDuration;
  final String setValue;

  const RadioHelper(
      {super.key, required this.selectedDuration, required this.setValue});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          onTap: () {
            selectedDuration.value = setValue;
          },
          child:
              TypoGraphy.text(setValue, level: 1, fontWeight: FontWeight.w500),
        ),
        Radio<String>(
          activeColor: ColorConstant.signUp,
          value: setValue,
          groupValue: selectedDuration.value,
          onChanged: (value) {
            selectedDuration.value = value!;
          },
        ),
      ],
    );
  }
}

class DaysContainer extends StatelessWidget {
  final String title;
  final ValueNotifier<bool> isSelected;
  final VoidCallback onTap;

  const DaysContainer(
      {super.key,
      required this.title,
      required this.isSelected,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: isSelected,
      builder: (context, value, child) => GestureDetector(
        onTap: () {
          isSelected.value = !isSelected.value;
          onTap();
        },
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                value
                    ? ColorConstant.gradient1
                    : ColorConstant.skyBlue1.withOpacity(0.5),
                value
                    ? ColorConstant.gradient2
                    : ColorConstant.skyBlue1.withOpacity(0.5),
              ],
            ),
            borderRadius: const BorderRadius.all(Radius.circular(4)),
          ),
          height: MySize.getScaledSizeHeight(92),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Align(
                alignment: Alignment.topLeft,
                child: Padding(
                  padding: const EdgeInsets.only(left: 10.0, top: 10.0),
                  child: ValueListenableBuilder(
                      valueListenable: isSelected,
                      builder: (context, value, child) => TypoGraphy.text(
                            title,
                            level: 2,
                            color: value
                                ? ColorConstant.white
                                : ColorConstant.black,
                            fontWeight: FontWeight.w500,
                          )),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    // return Card(
    //   borderOnForeground: true,
    //   color: ColorConstant.dashBoardTextField,
    //   elevation: 0,
    //   shape: RoundedRectangleBorder(
    //     side: const BorderSide(color: ColorConstant.textFieldColor),
    //     borderRadius: BorderRadius.only(
    //         bottomLeft: Radius.circular(bottomLeft),
    //         bottomRight: Radius.circular(bottomRight),
    //         topLeft: Radius.circular(topLeft),
    //         topRight: Radius.circular(topRight)),
    //   ),
    //   clipBehavior: Clip.antiAlias,
    //   margin: EdgeInsets.zero,
    //   child: Row(
    //     children: [
    //       TypoGraphy.text(date, level: 1),
    //       const Expanded(child: SizedBox.shrink()),
    //       GradientText(
    //           gradient: const LinearGradient(colors: [
    //             ColorConstant.white,
    //             ColorConstant.primaryColor,
    //           ]),
    //           child: TypoGraphy.text("₹ $amount",
    //               color: ColorConstant.primaryColor,
    //               level: 1,
    //               fontWeight: FontWeight.w500))
    //     ],
    //   ),
    //   // child: ExpansionTile(
    //   //   shape: OutlineInputBorder(
    //   //     borderSide: const BorderSide(color: ColorConstant.dashBoardTextField),
    //   //     borderRadius: BorderRadius.only(
    //   //         bottomLeft: Radius.circular(bottomLeft),
    //   //         bottomRight: Radius.circular(bottomRight),
    //   //         topLeft: Radius.circular(topLeft),
    //   //         topRight: Radius.circular(topRight)),
    //   //   ),
    //   //   collapsedBackgroundColor: ColorConstant.textFieldColor,
    //   //   backgroundColor: ColorConstant.textFieldColor,
    //   //   title: Row(
    //   //     children: [
    //   //       TypoGraphy.text(date, level: 1),
    //   //       const Expanded(child: SizedBox.shrink()),
    //   //       GradientText(
    //   //           gradient: const LinearGradient(colors: [
    //   //             ColorConstant.white,
    //   //             ColorConstant.primaryColor,
    //   //           ]),
    //   //           child: TypoGraphy.text("₹ $amount",
    //   //               color: ColorConstant.primaryColor,
    //   //               level: 1,
    //   //               fontWeight: FontWeight.w500))
    //   //     ],
    //   //   ),
    //   //   children: [
    //   //     Padding(
    //   //       padding: EdgeInsets.only(bottom: MySize.getScaledSizeHeight(20)),
    //   //       child: Container(
    //   //         width: MySize.getScaledSizeWidth(327),
    //   //         height: MySize.getScaledSizeHeight(50),
    //   //         decoration: const BoxDecoration(
    //   //             borderRadius: BorderRadius.all(Radius.circular(25)),
    //   //             color: ColorConstant.white),
    //   //         child: Padding(
    //   //           padding: EdgeInsets.symmetric(
    //   //               horizontal: MySize.getScaledSizeWidth(8)),
    //   //           child: Row(
    //   //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //   //             children: [
    //   //               TypoGraphy.text("Paid",
    //   //                   level: 1,
    //   //                   fontWeight: FontWeight.w700,
    //   //                   color: ColorConstant.presentDarkColor),
    //   //               TypoGraphy.text("23/04/2024",
    //   //                   level: 1, fontWeight: FontWeight.w500)
    //   //             ],
    //   //           ),
    //   //         ),
    //   //       ),
    //   //     )
    //   //   ],
    //   // ),
    // );
  }
}

class SportContainer extends StatelessWidget {
  final ValueNotifier<bool> isSelected;
  final VoidCallback onTap;
  final String title;
  final bool isCustom;
  final String image, selectedImage;
  final VoidCallback? editTap;

  const SportContainer(
      {super.key,
      required this.title,
      required this.selectedImage,
      required this.image,
      this.editTap,
      this.isCustom = false,
      required this.isSelected,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: isSelected,
      builder: (context, value, child) => GestureDetector(
        onTap: () {
          onTap();
        },
        child: Container(
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.all(Radius.circular(6)),
            gradient: LinearGradient(
              colors: [
                value
                    ? ColorConstant.gradient1
                    : ColorConstant.skyBlue1.withOpacity(0.5),
                value
                    ? ColorConstant.gradient2
                    : ColorConstant.skyBlue1.withOpacity(0.5),
              ],
            ),
          ),
          width: double.infinity,
          height: MySize.getScaledSizeHeight(100),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Align(
                    alignment: Alignment.topLeft,
                    child: Padding(
                        padding: EdgeInsets.only(
                            left: MySize.getScaledSizeWidth(10),
                            top: MySize.getScaledSizeHeight(10)),
                        child: TypoGraphy.text(
                          title,
                          level: 2,
                          color:
                              value ? ColorConstant.white : ColorConstant.black,
                          fontWeight: FontWeight.w500,
                        )),
                  ),
                  if (isCustom)
                    GestureDetector(
                      onTap: () {
                        editTap!();
                      },
                      child: Padding(
                        padding: EdgeInsets.only(
                            right: MySize.getScaledSizeWidth(10),
                            top: MySize.getScaledSizeHeight(8)),
                        child: CustomSvgPicture(AppAsset.editPencil,
                            color: isSelected.value
                                ? ColorConstant.white
                                : ColorConstant.primaryColor),
                      ),
                    )
                ],
              ),
              Align(
                  alignment: Alignment.bottomRight,
                  child: CustomSvgPicture(
                    value ? image : selectedImage,
                    height: MySize.getScaledSizeHeight(55),
                    color: ColorConstant.skyBlue1,
                  )),
            ],
          ),
        ),
      ),
    );
  }
}

class SportModel {
  final String title;
  bool isCustom, isSelected;
  final String image, selectedImage;

  SportModel(
      {required this.title,
      required this.image,
      required this.selectedImage,
      this.isCustom = false,
      this.isSelected = false});
}
